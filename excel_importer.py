# Excel导入模块

import pandas as pd
import datetime
import os
from openpyxl import load_workbook

class ExcelImporter:
    def __init__(self, file_path):
        self.file_path = file_path
        self.valid = False
        self.data = []
    
    def validate_format(self):
        """验证Excel文件格式是否符合要求"""
        try:
            if not os.path.exists(self.file_path):
                print(f"文件不存在: {self.file_path}")
                return False
            
            # 使用openpyxl加载工作簿以检查表格基本信息
            wb = load_workbook(self.file_path, data_only=True)
            sheet = wb.active
            
            # 检查表格是否有足够的行数
            if sheet.max_row < 2:
                print(f"表格行数不足: 只有{sheet.max_row}行")
                return False
                
            # 尝试查找表头行
            header_row = None
            header_columns = ['序号', '生产订单编号', '产品名称', '项目号', '图号/型号', '材料', '生产数量','存储地点','计划开工日']
            
            # 在前20行中查找表头
            for row in range(1, min(20, sheet.max_row + 1)):
                # 获取当前行的值
                row_values = [sheet.cell(row=row, column=col).value for col in range(1, sheet.max_column + 1)]
                # 检查是否包含关键列名
                if '生产订单编号' in row_values and '产品名称' in row_values:
                    header_row = row
                    print(f"找到表头行: 第{header_row}行")
                    break
            
            if header_row is None:
                print("未找到包含必要列名的表头行")
                return False
            
            # 保存表头行号，供后续提取数据使用
            self.header_row = header_row
            self.valid = True
            return True
        except Exception as e:
            print(f"验证Excel格式错误: {e}")
            return False
    
    def extract_data(self):
        """从Excel文件中提取数据"""
        try:
            if not self.valid and not self.validate_format():
                return False
            
            # 使用pandas读取Excel文件，使用validate_format中找到的表头行
            if not hasattr(self, 'header_row'):
                print("未找到有效的表头行，请先调用validate_format方法")
                return False
                
            print(f"使用第{self.header_row}行作为表头读取数据")
            df = pd.read_excel(self.file_path, header=self.header_row-1)  # pandas的header是0-based
            
            # 检查是否包含必要的列
            required_columns = ['生产订单编号', '产品名称', '项目号', '图号/型号', '材料', '生产数量','存储地点','计划开工日']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"表格缺少必要的列: {missing_columns}")
                print(f"现有列: {list(df.columns)}")
                return False
            
            # 获取当前日期作为接单日期
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            
            # 提取有效数据（生产订单编号不为空的行）
            valid_data = []
            for _, row in df.iterrows():
                # 检查生产订单编号是否为空
                if pd.notna(row['生产订单编号']):
                    # 创建数据映射
                    print(f"当前列顺序: {list(df.columns)}")
                    order_data = {
                        'mo_number': row['生产订单编号'] if pd.notna(row['生产订单编号']) else '',
                        'name': row['产品名称'] if pd.notna(row['产品名称']) else '',
                        'project_number': row['项目号'] if pd.notna(row['项目号']) else '',
                        'drawing_number': row['图号/型号'] if pd.notna(row['图号/型号']) else '',
                        'material': row['材料'] if pd.notna(row['材料']) else '',
                        'quantity': int(row['生产数量']) if pd.notna(row['生产数量']) else '',
                        'shipping_location': row['存储地点'] if pd.notna(row['存储地点']) else '',#新增存储地点--发货地
                        'order_date': pd.to_datetime(row['计划开工日']).strftime('%Y-%m-%d') if pd.notna(row['计划开工日']) else ''
                    }
                    print(f"实际列名: {list(df.columns)}")
                    
                    # 处理交期日期格式
                    if '计划完工日' in df.columns and pd.notna(row['计划完工日']):
                        try:
                            # 尝试将日期转换为标准格式
                            delivery_date = pd.to_datetime(row['计划完工日']).strftime("%Y-%m-%d")
                            order_data['delivery_date'] = delivery_date
                        except Exception as e:
                            print(f"日期格式转换错误: {e}")
                            # 如果转换失败，使用空字符串
                            order_data['delivery_date'] = ''
                    else:
                        order_data['delivery_date'] = ''
                    
                    valid_data.append(order_data)
            
            if not valid_data:
                print("未找到有效的生产订单编号数据")
                print(f"表格行数: {len(df)}")
                if len(df) > 0:
                    print(f"生产订单编号列非空值数量: {df['生产订单编号'].notna().sum()}")
            
            self.data = valid_data
            return len(valid_data) > 0
        except Exception as e:
            print(f"提取Excel数据错误: {e}")
            return False
    
    def get_data(self):
        """获取提取的数据"""
        if not self.data:
            print("警告: 没有有效数据可返回")
        else:
            print(f"返回{len(self.data)}条有效数据")
        return self.data
        
    def import_from_file(self):
        """导入Excel文件并返回数据"""
        if not self.validate_format():
            raise Exception("Excel文件格式验证失败")
            
        if not self.extract_data():
            raise Exception("Excel数据提取失败")
            
        return self.get_data()