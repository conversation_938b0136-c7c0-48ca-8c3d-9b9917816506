# 机械加工管理系统

## 项目概述

这是一个基于PyQt5和MySQL的机械加工管理系统，用于管理机械加工订单的全生命周期，包括接单、加工、交付等环节。

## 功能特点

- Excel表格导入功能，支持从表格中导入订单数据
- 订单状态管理：接单、加工完成、交付完成
- 数据编辑功能，支持修改订单信息
- 删除记录日志，可查询已删除的历史数据
- 表格排序功能，方便数据查看和管理

## 安装步骤

1. 确保已安装Python 3.6+
2. 安装所需依赖包：

```bash
pip install -r requirements.txt
```

3. 配置MySQL数据库：
   - 创建名为`machine_manage`的数据库
   - 修改`config.py`中的数据库连接信息

## 使用方法

1. 运行主程序：

```bash
python main.py
```

2. 导入Excel表格：
   - 点击「导入表格」按钮
   - 选择符合格式要求的Excel文件
   - 系统会自动验证格式并导入有效数据

3. 订单管理：
   - 选中订单行（勾选复选框）
   - 点击「加工完成」标记加工状态
   - 点击「交付完成」标记交付状态
   - 点击「删除」删除选中订单

4. 查看删除日志：
   - 点击「查询日志」按钮查看已删除的订单记录

## 数据表格格式要求

导入的Excel表格必须符合以下格式要求：
- B13单元格内容为"序号"
- C13单元格内容为"生产订单编号"
- D13单元格内容为"产品编码"
- F13单元格内容为"产品名称"
- G13单元格内容为"项目号"
- H13单元格内容为"图号/型号"
- I13单元格内容为"材料"
- J13单元格内容为"生产数量"
- K13单元格内容为"MRP数量"
- N13单元格内容为"生产单位"
- R13单元格内容为"存储地点"
- S13单元格内容为"计划开工日"
- U13单元格内容为"计划完工日"

## 系统要求

- Python 3.6+
- MySQL 5.7+
- PyQt5
- pandas
- openpyxl
- pymysql