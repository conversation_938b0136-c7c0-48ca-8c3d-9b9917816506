我需要开发一个python小程序，用pyqt5作为UI界面；mysql数据库进行数据管理：
1、小程序主窗口的header部分，就是和表2中的A1~W1的表头内容一致，总共23列内容；要求严格按照表2中表头的内容和顺序；
2、创建mysql数据库表，字段就对应上面23列内容；
3、主窗口的最上方，从左往右依次布置“导入表格”、“加工完成”、“交付完成”、“删除”、“查询日志”几个操作按钮；
4、点击导入表格后，可以导入与表1相同格式的表格文件，导入时只需要匹配以下规则即可认为格式正确：
   单元格B13内容为“序号”、单元格C13内容为“生产订单编号”、单元格D13内容为“产品编码”、单元格F13内容为“产品名称”、单元格G13内容为“项目号”、单元格H13内容为“图号/型号”、单元格I13内容为“材料”、单元格J13内容为“生产数量”、单元格K13内容为“MRP数量”、单元格N13内容为“生产单位”、单元格R13内容为“存储地点”、单元格S13内容为“计划开工日”、单元格U13内容为“计划完工日”;
如果格式不正确，则弹窗提示：“表格内容格式不正确”；
5、导入表格时，需要将部分表格中的内容，引入到对应表头的数据库表的字段中，映射关系如下：
   表格的“生产订单编号”列，对应表头的"MO编号"；
   表格的“产品名称”列，对应表头的"名称"；
   表格的“项目号”列，对应表头的"项目号"；
   表格的“图号/型号”列，对应表头的"图号"；
   表格的“材料”列，对应表头的"材质"；
   表格的“生产数量”列，对应表头的"数量"；
   表格的“计划完工日”列，对应表头的"交期日期"；需要将格式转换为“2023-09-09”；
6、导入表格时，只需要将表格中有效的内容导入到数据库表中即可；有效的内容判定规则是：
   表格的“生产订单编号”列，不为空；
7、导入表格时，自动将系统当前日期，填入到“接单日期”字段中；格式为“2023-09-09”；
8、主窗口的内容部分，就是和表2中的A2~W10000的内容一致，总共23列内容；
9、主窗口内容的每一行最左边，都是一个复选框；可以选中多行；
10、当点击“加工完成”按钮时，会将系统的当前时间，填入到选中行的“加工完成时间”字段中；格式为“2023-09-09 10:10:10”；
11、当点击“交付完成”按钮时，会将系统的当前时间，填入到选中行的“入库日期”字段中；格式为“2023-09-09 10:10:10”；
12、表头的“是否来料”这一列的内容，支持下拉选择，选择“是”或“否”或空值；
13、表头的其它列的内容，需要人工输入，支持输入中文、英文、数字、特殊字符；
14、当点击“删除”按钮时，会将选中行的数据，从数据库表中删除；窗口会自动刷新，并提示：“删除成功”；
15、当点击“查询日志”按钮时，会弹出一个新的窗口，显示曾经被删除的数据和删除操作的时间；
16、主窗口的header部分，和内容部分，都是可以排序的；
主画面左右侧，在“备注”的后面，增加4列，分别是：预估CNC时间、预估车床时间、预估线割时间、状态。
其中预估CNC时间、预估车床时间、预估线割时间，这3列内容对应的数据字段格式为float，保留小数点后面2位；
状态这列的内容，对应的数据字段为ENUM(枚举类型有待加工、已完成、NULL)
预估CNC时间、预估车床时间、预估线割时间这三列，最下面分别有当前页面所有行的汇总求和；
这4列，只有管理员账号能看到，其它账号登录则不显示；