# UI界面模块

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, 
                             QFileDialog, QMessageBox, QComboBox, QCheckBox, QDialog,
                             QLabel, QLineEdit, QAbstractItemView, QMenu, QAction,
                             QListWidget, QListWidgetItem, QScrollArea, QFrame, QToolButton,
                             QStyleFactory, QStyledItemDelegate, QStyle)
from PyQt5.QtCore import (Qt, QDateTime, QEvent, QSize, QPoint, QRect, QTimer, 
                         QSettings, QThread, pyqtSignal)
from PyQt5.QtGui import (QIcon, QCursor, QFont, QColor, QPainter, QLinearGradient, QPen, 
                        QBrush, QPalette, QFontMetrics, QPainterPath, QIntValidator)
from database import Database
from excel_importer import ExcelImporter

class FilterButton(QToolButton):
    """自定义3D效果筛选按钮"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(18, 18)
        self.setCursor(Qt.PointingHandCursor)
        self.is_active = False
        self.is_hovered = False
        
    def paintEvent(self, event):
        """自定义绘制"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置按钮背景
        if self.is_active:
            # 激活状态 - 蓝色背景
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0, QColor(160, 210, 250))
            gradient.setColorAt(1, QColor(120, 180, 240))
            painter.setBrush(QBrush(gradient))
        elif self.is_hovered:
            # 悬停状态 - 浅灰色背景
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0, QColor(240, 240, 240))
            gradient.setColorAt(1, QColor(220, 220, 220))
            painter.setBrush(QBrush(gradient))
        else:
            # 普通状态 - 几乎透明
            painter.setBrush(Qt.transparent)
        
        # 绘制圆角背景
        if self.is_active or self.is_hovered:
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(0, 0, self.width(), self.height(), 3, 3)
        
        # 绘制三角形
        painter.setPen(QPen(QColor(80, 80, 80), 1.2))
        
        # 计算三角形位置 (居中)
        triangle_width = 8
        triangle_height = 5
        x_start = (self.width() - triangle_width) // 2
        y_mid = self.height() // 2 + 1
        
        # 绘制带有3D效果的三角形
        # 1. 先绘制三角形底部的阴影，偏移一个像素
        shadow_color = QColor(60, 60, 60, 120)
        painter.setPen(QPen(shadow_color, 1.2))
        shadow_path = QPainterPath()
        shadow_path.moveTo(x_start + 1, y_mid - 2)
        shadow_path.lineTo(x_start + triangle_width + 1, y_mid - 2)
        shadow_path.lineTo(x_start + triangle_width/2 + 1, y_mid + triangle_height - 2)
        shadow_path.lineTo(x_start + 1, y_mid - 2)
        painter.drawPath(shadow_path)
        
        # 2. 再绘制三角形本身
        if self.is_active:
            triangle_color = QColor(255, 255, 255)
        else:
            triangle_color = QColor(80, 80, 80)
            
        painter.setPen(QPen(triangle_color, 1.2))
        path = QPainterPath()
        path.moveTo(x_start, y_mid - 3)
        path.lineTo(x_start + triangle_width, y_mid - 3)
        path.lineTo(x_start + triangle_width/2, y_mid + triangle_height - 3)
        path.lineTo(x_start, y_mid - 3)
        painter.drawPath(path)

    def set_active(self, active):
        """设置按钮为激活状态"""
        self.is_active = active
        self.update()
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self.update()
        super().leaveEvent(event)
        
class HeaderItemDelegate(QStyledItemDelegate):
    """自定义表头代理，用于3D绘制"""
    def paint(self, painter, option, index):
        """自定义绘制表头项"""
        painter.save()
        
        # 绘制背景渐变
        header_gradient = QLinearGradient(0, 0, 0, option.rect.height())
        header_gradient.setColorAt(0, QColor(250, 250, 250))
        header_gradient.setColorAt(1, QColor(230, 230, 230))
        
        painter.fillRect(option.rect, header_gradient)
        
        # 绘制边框
        painter.setPen(QPen(QColor(210, 210, 210), 1))
        painter.drawLine(option.rect.bottomLeft(), option.rect.bottomRight())
        painter.drawLine(option.rect.topRight(), option.rect.bottomRight())
        
        # 绘制文本
        text = index.data(Qt.DisplayRole)
        if text:
            font = painter.font()
            font.setBold(True)
            painter.setFont(font)
            
            # 文本阴影效果 (轻微偏移)
            painter.setPen(QPen(QColor(150, 150, 150, 40)))
            text_rect = option.rect.adjusted(6, 1, -25, -1)  # 留出右侧空间给筛选按钮
            painter.drawText(text_rect.adjusted(1, 1, 1, 1), Qt.AlignLeft | Qt.AlignVCenter, text)
            
            # 实际文本
            painter.setPen(QPen(QColor(70, 70, 70)))
            painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)
        
        painter.restore()

class FilterHeaderView(QHeaderView):
    """支持过滤功能的表格"""
    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        
        # 存储每列的过滤状态
        self.filters = {}
        self.filter_buttons = {}
        # 存储每列的所有唯一值（即使被筛选掉也保留）
        self.all_column_values = {}
        
        self.setSectionsClickable(True)
        self.sectionClicked.connect(self.handle_section_clicked)
        
        # 设置表头样式，为过滤按钮留出空间
        self.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.setStretchLastSection(True)
        
        # 设置3D样式
        self.setStyleSheet("""
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #FAFAFA, stop:1 #E6E6E6);
                border: 1px solid #D2D2D2;
                border-left: none;
                padding-left: 4px;
                padding-right: 20px; /* 为筛选按钮留出空间 */
                font-weight: bold;
                color: #424242;
                min-height: 28px;
            }
            QHeaderView::section:first {
                border-left: 1px solid #D2D2D2;
            }
        """)
        
        # 设置代理
        self.setItemDelegate(HeaderItemDelegate())
        
        # 安装事件过滤器，监听视口事件
        self.viewport().installEventFilter(self)
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于捕获视口事件"""
        if obj is self.viewport():
            # 如果是调整大小、移动等可能影响按钮位置的事件
            if event.type() in [QEvent.Resize, QEvent.Move, QEvent.Paint, QEvent.Show]:
                # 使用QTimer确保在事件处理后更新按钮
                QTimer.singleShot(0, self.update_filter_buttons)
                
        return super().eventFilter(obj, event)
    
    def showEvent(self, event):
        """显示事件，确保按钮正确放置"""
        super().showEvent(event)
        self.update_filter_buttons()
    
    def sectionResized(self, logicalIndex, oldSize, newSize):
        """部分大小调整时更新按钮位置"""
        super().sectionResized(logicalIndex, oldSize, newSize)
        # 当列宽调整时，立即更新按钮位置
        self.update_filter_buttons()
        
        # 如果是交互式调整列宽，确保在操作完成后重新布局
        if self.isInteractive():
            QTimer.singleShot(10, self.update_filter_buttons)
    
    def resizeEvent(self, event):
        """窗口大小调整时更新所有按钮位置"""
        super().resizeEvent(event)
        self.update_filter_buttons()
    
    def update_filter_buttons(self):
        """更新所有过滤按钮位置"""
        if not self.viewport() or not self.isVisible():
            return
            
        for section in range(self.count()):
            # 跳过第一列（复选框列）
            if section == 0:
                continue
            
            if section not in self.filter_buttons:
                # 创建3D效果的过滤按钮
                button = FilterButton(self.viewport())
                button.setToolTip("点击筛选")
                button.clicked.connect(lambda checked, s=section: self.show_filter_menu(s))
                self.filter_buttons[section] = button
                
                # 如果该列已经有筛选条件，设置为激活状态
                if section in self.filters:
                    items = self.filters[section]
                    # 如果不是所有值都被选中，说明有筛选条件
                    if len(items) < len(self.all_column_values.get(section, {}).get('values', set())):
                        button.set_active(True)
            
            # 获取按钮
            button = self.filter_buttons[section]
            
            # 计算按钮位置
            section_pos = self.sectionViewportPosition(section)
            section_width = self.sectionSize(section)
            
            # 确保按钮位于该部分的最右侧，考虑滚动条位置
            pos_x = section_pos + section_width - button.width() - 5
            pos_y = (self.height() - button.height()) // 2
            
            # 如果列不在可见区域内，隐藏按钮
            if pos_x < 0 or pos_x + button.width() > self.viewport().width() or section_pos > self.viewport().width():
                button.hide()
            else:
                # 更新按钮位置并显示
                button.move(pos_x, pos_y)
                button.show()
                button.raise_()  # 确保按钮在最顶层
    
    def handle_section_clicked(self, section):
        """处理表头点击事件"""
        # 如果点击了过滤按钮区域，则不执行排序
        if section in self.filter_buttons:
            button = self.filter_buttons[section]
            click_pos = self.mapFromGlobal(QCursor.pos())
            button_rect = QRect(button.pos().x(), button.pos().y(), button.width(), button.height())
            
            if button_rect.contains(click_pos):
                return
    
    def show_filter_menu(self, section):
        """显示指定列的过滤菜单"""
        table = self.parentWidget()
        if not table or not isinstance(table, QTableWidget):
            return
        
        # 每次打开筛选菜单时重新收集所有唯一值，确保包含最新内容
        self.all_column_values[section] = self.collect_all_values(table, section)
            
        # 获取该列的所有唯一值（包括隐藏行的值）
        unique_values = self.all_column_values[section]['values']
        has_empty = self.all_column_values[section]['has_empty']
        
        # 显示过滤弹窗
        button = self.filter_buttons[section]
        popup = FilterPopup(table, section, unique_values, has_empty)
        
        # 如果已有过滤设置，则恢复
        if section in self.filters:
            checked_items = self.filters[section]
            popup.checked_items = checked_items
            popup.has_empty_checked = "(空" in checked_items
            
            for value, cb in popup.checkboxes.items():
                cb.setChecked(value in checked_items)
            
            popup.selectAll.setChecked(len(checked_items) == len(unique_values))
        
        # 计算弹窗位置
        pos = button.mapToGlobal(QPoint(0, button.height()))
        popup.move(pos)
        
        # 执行弹窗
        result = popup.exec_()
        if result == QDialog.Accepted:
            # 应用过滤
            self.filters[section] = popup.checked_items
            self.apply_filters(table)
            
            # 更新按钮激活状态
            if len(popup.checked_items) < len(unique_values):
                button.set_active(True)
            else:
                button.set_active(False)
    
    def collect_all_values(self, table, section):
        """收集表格指定列的所有唯一值，包括可能隐藏的行"""
        # 暂时记住当前的行可见状态
        row_hidden_states = []
        for row in range(table.rowCount()):
            row_hidden_states.append(table.isRowHidden(row))
            # 临时显示所有行，以便收集所有值
            table.setRowHidden(row, False)
        
        # 收集所有唯一值
        unique_values = set()
        has_empty = False
        
        for row in range(table.rowCount()):
            item = table.item(row, section)
            widget = table.cellWidget(row, section)
            
            if item and item.text():
                unique_values.add(item.text())
            elif widget and isinstance(widget, QComboBox) and widget.currentText():
                unique_values.add(widget.currentText())
            else:
                has_empty = True
        
        # 恢复行的可见状态
        for row in range(table.rowCount()):
            table.setRowHidden(row, row_hidden_states[row])
        
        # 如果有空值，添加一个特殊的"(空"选项
        if has_empty:
            unique_values.add("(空")
            
        return {'values': unique_values, 'has_empty': has_empty}
    
    def apply_filters(self, table):
        """应用所有过滤条件"""
        # 首先显示所有行，确保不会漏掉行
        for row in range(table.rowCount()):
            table.setRowHidden(row, False)
            
        # 然后根据过滤条件隐藏不需要的行    
        for row in range(table.rowCount()):
            should_show = True
            
            # 检查每个过滤列
            for section, checked_items in self.filters.items():
                value = ""
                item = table.item(row, section)
                widget = table.cellWidget(row, section)
                
                if item and item.text():
                    value = item.text()
                elif widget and isinstance(widget, QComboBox) and widget.currentText():
                    value = widget.currentText()
                
                # 检查空值情况
                if not value:
                    if "(空" not in checked_items:
                        should_show = False
                        break
                # 检查正常情况
                elif value not in checked_items:
                    should_show = False
                    break
            
            # 设置行可见状态
            table.setRowHidden(row, not should_show)
            
        # 过滤条件应用后，更新汇总行
        main_window = table.window()
        if hasattr(main_window, 'update_time_summary'):
            main_window.update_time_summary()
    
    def clear_filters(self):
        """清除所有过滤"""
        self.filters.clear()
        for button in self.filter_buttons.values():
            if isinstance(button, FilterButton):
                button.set_active(False)
        
        # 显示所有行
        table = self.parentWidget()
        if table and isinstance(table, QTableWidget):
            for row in range(table.rowCount()):
                table.setRowHidden(row, False)

class FilterPopup(QDialog):
    """过滤弹窗"""
    def __init__(self, parent, column, unique_values, has_empty=False):
        super().__init__(parent, Qt.Popup)
        self.column = column
        self.unique_values = unique_values
        self.checked_items = set(unique_values)  # 初始时所有项都被选中
        self.has_empty = has_empty
        self.has_empty_checked = has_empty  # 初始时空值被选中
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        self.setStyleSheet("""
            QDialog {
                background-color: #FFFFFF;
                border: 1px solid #CCCCCC;
                border-radius: 4px;
            }
            QCheckBox {
                padding: 3px;
                border-radius: 2px;
            }
            QCheckBox:hover {
                background-color: #F0F8FF;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #F6F6F6, stop:1 #E6E6E6);
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                min-height: 24px;
                padding: 0 10px;
                color: #333333;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #FFFFFF, stop:1 #F0F0F0);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #E0E0E0, stop:1 #D0D0D0);
            }
            QScrollArea {
                border: 1px solid #DDDDDD;
                border-radius: 2px;
                background-color: #FAFAFA;
            }
            QLineEdit {
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                padding: 3px;
                background-color: #FFFFFF;
            }
            QLineEdit:focus {
                border: 1px solid #90CAF9;
            }
        """)
        
        # 添加搜索框
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索...")
        self.search_input.textChanged.connect(self.filter_items)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # "全选复选框
        self.selectAll = QCheckBox("全选")
        self.selectAll.setChecked(True)
        font = self.selectAll.font()
        font.setBold(True)
        self.selectAll.setFont(font)
        self.selectAll.stateChanged.connect(self.select_all_changed)
        layout.addWidget(self.selectAll)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 滚动区域，包含所有唯一值的复选框
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_content)
        
        # 添加数据项复选框
        self.checkboxes = {}
        
        # 将值列表转换为列表并排序（将空值放在最上方）
        self.values_list = sorted([v for v in self.unique_values if v != "(空"])
        if "(空" in self.unique_values:
            self.values_list.insert(0, "(空")
        
        for value in self.values_list:
            cb = QCheckBox(str(value))
            cb.setChecked(True)
            cb.stateChanged.connect(self.item_changed)
            self.scroll_layout.addWidget(cb)
            self.checkboxes[value] = cb
        
        self.scroll_area.setWidget(self.scroll_content)
        layout.addWidget(self.scroll_area)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        self.apply_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        
        self.apply_btn.clicked.connect(self.apply_filter)
        self.cancel_btn.clicked.connect(self.cancel_filter)
        
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 设置窗口大小
        self.setMinimumWidth(240)
        self.setMaximumHeight(400)
    
    def filter_items(self, text):
        """根据搜索文本过滤项目"""
        search_text = text.lower()
        
        # 隐藏所有复选框
        for cb in self.checkboxes.values():
            cb.setVisible(False)
        
        # 如果搜索文本为空，显示所有复选框
        if not search_text:
            for cb in self.checkboxes.values():
                cb.setVisible(True)
            return
        
        # 显示匹配的复选框
        for value, cb in self.checkboxes.items():
            if search_text in str(value).lower():
                cb.setVisible(True)
    
    def select_all_changed(self, state):
        """全选状态改变"""
        for cb in self.checkboxes.values():
            cb.blockSignals(True)
            cb.setChecked(state == Qt.Checked)
            cb.blockSignals(False)
        
        if state == Qt.Checked:
            self.checked_items = set(self.unique_values)
            self.has_empty_checked = self.has_empty
        else:
            self.checked_items = set()
            self.has_empty_checked = False
    
    def item_changed(self, state):
        """单个项目状态改变"""
        sender = self.sender()
        value = sender.text()
        
        if state == Qt.Checked:
            self.checked_items.add(value)
            if value == "(空":
                self.has_empty_checked = True
        else:
            self.checked_items.discard(value)
            if value == "(空":
                self.has_empty_checked = False
        
        # 更新"全选状态"
        if len(self.checked_items) == len(self.unique_values):
            self.selectAll.blockSignals(True)
            self.selectAll.setChecked(True)
            self.selectAll.blockSignals(False)
        else:
            self.selectAll.blockSignals(True)
            self.selectAll.setChecked(False)
            self.selectAll.blockSignals(False)
    
    def apply_filter(self):
        """应用过滤条件"""
        self.accept()
    
    def cancel_filter(self):
        """取消过滤"""
        self.reject()

class DeleteLogDialog(QDialog):
    """删除日志对话框"""
    def __init__(self, parent=None):
        super().__init__(parent, Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        self.setWindowTitle("删除日志")
        self.resize(1000, 600)
        self.db = Database()
        
        # 创建设置对象，用于保存/加载表格列宽
        self.settings = QSettings("UPTEC", "MachiningOrderManagement")
        
        self.init_ui()
        self.load_logs()
        
        # 加载保存的列宽
        self.load_column_widths()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建表格
        self.log_table = QTableWidget()
        # 设置列数为25，确保能显示所有字段
        self.log_table.setColumnCount(25)
        
        # 设置表头
        headers = [
            "记录ID", "MO编号", "项目编号", "名称", "图号", "数量", "材质", 
            "下料尺寸", "下料工艺要求", "CNC工时", "CNC编号", "车床时间", "慢丝时间", "快丝时间", 
            "接单日期", "交期日期", "来料需求日期", "是否来料", "加工完成时间", "入库日期", "发货地点", "快递", "备注", 
            "删除时间", "删除人"
        ]
        self.log_table.setHorizontalHeaderLabels(headers)
        
        # 禁用排序，以避免加载数据时的排序影响
        self.log_table.setSortingEnabled(False)
        
        # 设置支持筛选的表头
        filter_header = FilterHeaderView(Qt.Horizontal, self.log_table)
        self.log_table.setHorizontalHeader(filter_header)
        
        # 调整表格 - 修改为可交互调整列宽
        self.log_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置初始列宽
        default_widths = {
            0: 70,   # 记录ID
            1: 100,  # MO编号
            2: 100,  # 项目编号
            3: 120,  # 名称
            4: 100,  # 图号
            5: 60,   # 数量
            6: 100,  # 材质
            7: 120,  # 下料尺寸
            8: 150,  # 下料工艺要求
            9: 80,   # CNC工时
            10: 80,  # CNC编号
            11: 80,  # 车床时间
            12: 80,  # 慢丝时间
            13: 80,  # 快丝时间
            14: 100, # 接单日期
            15: 100, # 交期日期
            16: 100, # 来料需求日期
            17: 80,  # 是否来料
            18: 120, # 加工完成时间
            19: 120, # 入库日期
            20: 100, # 发货地点
            21: 80,  # 快递
            22: 120, # 备注
            23: 120, # 删除时间
            24: 80   # 删除人
        }
        
        # 应用初始列宽
        for col, width in default_widths.items():
            if col < self.log_table.columnCount():
                self.log_table.setColumnWidth(col, width)
                
        # 启用表格最后一节拉伸填充空白区域
        self.log_table.horizontalHeader().setStretchLastSection(True)
        
        # 添加到布局
        layout.addWidget(self.log_table)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 添加空白区域
        button_layout.addStretch()
        
        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
    
    def load_logs(self):
        """加载删除日志"""
        logs = self.db.get_delete_logs()
        print(f"获取到 {len(logs) if logs else 0} 条删除日志")
        
        if logs:
            self.log_table.setRowCount(len(logs))
            
            # 显示第一条记录的字段名称
            if logs and len(logs) > 0:
                first_log = logs[0]
                print(f"日志记录字段({len(first_log.keys())}个: {list(first_log.keys())}")
                
                # 定义表格列与数据库字段的对应关系
                column_headers = [
                    "记录ID", "MO编号", "项目编号", "名称", "图号", "数量", "材质", 
                    "下料尺寸", "下料工艺要求", "CNC工时", "CNC编号", "车床时间", "慢丝时间", "快丝时间", 
                    "接单日期", "交期日期", "来料需求日期", "是否来料", "加工完成时间", "入库日期", "发货地点", "快递", "备注", 
                    "删除时间", "删除人"
                ]
                
                # 字段到列索引的映射
                field_map = {
                    'id': None,  # 不显示日志表的自增ID
                    'record_id': 0,
                    'mo_number': 1,
                    'project_number': 2,
                    'name': 3,
                    'drawing_number': 4,
                    'quantity': 5,
                    'material': 6,
                    'cutting_size': 7,
                    'cutting_process': 8,
                    'cnc_hours': 9,
                    'cnc_number': 10,
                    'lathe_hours': 11,
                    'slow_wire_hours': 12,
                    'fast_wire_hours': 13,
                    'order_date': 14,
                    'delivery_date': 15,
                    'material_required_date': 16,
                    'is_material_arrived': 17,
                    'processing_completion_time': 18,
                    'storage_date': 19,
                    'shipping_location': 20,
                    'express': 21,
                    'remark': 22,
                    'delete_time': 23,
                    'deleted_by': 24
                }
                
                print(f"开始填充表格，行数：{len(logs)}")
                
                # 填充表格数据
                for row_idx, log_entry in enumerate(logs):
                    # 获取记录ID和删除人信息用于调试
                    record_id = log_entry.get('record_id', 'unknown')
                    deleted_by = log_entry.get('deleted_by', 'not_set')
                    delete_time = log_entry.get('delete_time', 'unknown')
                    print(f"处理行 {row_idx}，记录ID: {record_id}, 删除人: {deleted_by}, 时间: {delete_time}")
                    
                    # 先为每一列创建空的单元格
                    for column_idx in range(self.log_table.columnCount()):
                        self.log_table.setItem(row_idx, column_idx, QTableWidgetItem(""))
                    
                    # 然后填充有数据的单元格
                    for field_name, value in log_entry.items():
                        if field_name in field_map and field_map[field_name] is not None:
                            column_idx = field_map[field_name]
                            if column_idx < self.log_table.columnCount():
                                # 将None和空字符串都显示为空
                                display_value = str(value) if value not in (None, "") else ""
                                if field_name == 'deleted_by':
                                    print(f"  设置删除人列 {column_idx}，值: '{display_value}'")
                                self.log_table.setItem(row_idx, column_idx, QTableWidgetItem(display_value))
                    
                    # 处理删除人列
                    deleted_by_column = field_map.get('deleted_by')
                    if deleted_by_column is not None:
                        # 直接检查该单元格的内容
                        deleted_by_item = self.log_table.item(row_idx, deleted_by_column)
                        deleted_by_text = deleted_by_item.text() if deleted_by_item else ""
                        
                        # 记录原始状态
                        print(f"  删除人列状态检查 - 行 {row_idx}, 列 {deleted_by_column}: '{deleted_by_text}'")
                        
                        # 如果为空，则设置为"系统"
                        if not deleted_by_text.strip():
                            print(f"  >>> 行 {row_idx} 删除人为空，设置为'系统'")
                            self.log_table.setItem(row_idx, deleted_by_column, QTableWidgetItem("系统"))
                
                print("表格数据填充完成")
                
                # 先确保所有行的删除人列都有值
                deleted_by_column = field_map.get('deleted_by')
                if deleted_by_column is not None:
                    for row in range(self.log_table.rowCount()):
                        deleted_by_item = self.log_table.item(row, deleted_by_column)
                        deleted_by_text = deleted_by_item.text() if deleted_by_item else ""
                        if not deleted_by_text.strip():
                            self.log_table.setItem(row, deleted_by_column, QTableWidgetItem("系统"))
                
                # 暂时禁用排序功能，避免对删除人的显示产生影响
                self.log_table.setSortingEnabled(False)
                
                # 更新过滤按钮 - 但不激活过滤功能
                self.log_table.horizontalHeader().update_filter_buttons()
                
                # 先应用基础排序（按删除时间降序）
                self.log_table.sortItems(23, Qt.DescendingOrder)  # 23是delete_time列的索引
                
                # 最后一次确保所有行都有删除人信息
                if deleted_by_column is not None:
                    for row in range(self.log_table.rowCount()):
                        deleted_by_item = self.log_table.item(row, deleted_by_column)
                        deleted_by_text = deleted_by_item.text() if deleted_by_item else ""
                        if not deleted_by_text.strip():
                            self.log_table.setItem(row, deleted_by_column, QTableWidgetItem("系统"))
                
                # 重新启用排序
                self.log_table.setSortingEnabled(True)
            else:
                QMessageBox.information(self, "提示", "没有删除日志记录")
        else:
            QMessageBox.information(self, "提示", "没有删除日志记录")
            self.log_table.setRowCount(0)

    def showEvent(self, event):
        """重写showEvent方法，每次显示对话框时刷新数据"""
        super().showEvent(event)
        # 清空当前表格
        self.log_table.setRowCount(0)
        
        # 禁用排序，以避免加载数据时的排序影响
        self.log_table.setSortingEnabled(False)
        
        # 重新加载日志
        self.load_logs()
        
        # 在数据加载完成后，确保所有行都有删除人信息
        # 获取删除人列的索引
        deleted_by_column = 24  # 删除人是第25列，索引为24
        for row in range(self.log_table.rowCount()):
            item = self.log_table.item(row, deleted_by_column)
            if not item or not item.text().strip():
                self.log_table.setItem(row, deleted_by_column, QTableWidgetItem("系统"))
        
        # 再次更新所有行，确保它们在过滤和排序后仍然有删除人信息
        QTimer.singleShot(100, self.ensure_all_rows_have_deleted_by)
        
        # 重新启用排序
        self.log_table.setSortingEnabled(True)
    
    def ensure_all_rows_have_deleted_by(self):
        """确保所有行都有删除人信息，即使在排序和过滤后"""
        # 获取删除人列的索引
        deleted_by_column = 24  # 删除人是第25列，索引为24
        for row in range(self.log_table.rowCount()):
            if not self.log_table.isRowHidden(row):
                item = self.log_table.item(row, deleted_by_column)
                if not item or not item.text().strip():
                    self.log_table.setItem(row, deleted_by_column, QTableWidgetItem("系统"))

    def save_column_widths(self):
        """保存表格列宽到用户配置"""
        if not self.log_table:
            return
            
        # 保存日志表格列宽
        for col in range(self.log_table.columnCount()):
            width = self.log_table.columnWidth(col)
            self.settings.setValue(f"LogTable/col{col}_width", width)
            
        # 保存窗口大小
        self.settings.setValue("LogDialog/size", self.size())
        self.settings.setValue("LogDialog/pos", self.pos())
    
    def load_column_widths(self):
        """从用户配置加载表格列宽"""
        if not self.log_table:
            return
            
        # 加载日志表格列宽
        for col in range(self.log_table.columnCount()):
            width_key = f"LogTable/col{col}_width"
            if self.settings.contains(width_key):
                width = self.settings.value(width_key, type=int)
                if width > 0:  # 确保宽度合理
                    self.log_table.setColumnWidth(col, width)
        
        # 加载窗口大小和位置
        if self.settings.contains("LogDialog/size"):
            size = self.settings.value("LogDialog/size")
            self.resize(size)
        
        if self.settings.contains("LogDialog/pos"):
            pos = self.settings.value("LogDialog/pos")
            self.move(pos)
    
    def closeEvent(self, event):
        """重写关闭事件，保存列宽和窗口大小"""
        self.save_column_widths()
        super().closeEvent(event)
        
    def accept(self):
        """点击确定按钮时保存列宽"""
        self.save_column_widths()
        super().accept()
        
    def reject(self):
        """点击取消按钮时保存列宽"""
        self.save_column_widths()
        super().reject()

class ComboBoxDelegate(QStyledItemDelegate):
    """用于创建下拉框的委托"""
    def __init__(self, items, parent=None):
        super().__init__(parent)
        self.items = items

    def createEditor(self, parent, option, index):
        editor = QComboBox(parent)
        editor.addItems(self.items)
        return editor

    def setEditorData(self, editor, index):
        value = index.model().data(index, Qt.EditRole)
        editor.setCurrentText(str(value))

    def setModelData(self, editor, model, index):
        value = editor.currentText()
        model.setData(index, value, Qt.EditRole)
        
    def paint(self, painter, option, index):
        """自定义绘制下拉框单元格，统一样式"""
        if not index.isValid():
            return super().paint(painter, option, index)
            
        # 保存画笔状态
        painter.save()
        
        # 绘制选中或悬停背景
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
            painter.setPen(option.palette.highlightedText().color())
        else:
            painter.fillRect(option.rect, option.palette.base())
            painter.setPen(option.palette.text().color())
            
        # 获取文本
        text = index.data(Qt.DisplayRole)
        if text is None:
            text = ""
        
        # 绘制文本（左对齐，垂直居中）
        painter.drawText(
            option.rect.adjusted(4, 0, -4, 0),  # 左边和右边留出4像素的边距
            Qt.AlignLeft | Qt.AlignVCenter,
            text
        )
        
        # 绘制下拉箭头指示器
        if not (option.state & QStyle.State_Editing):
            # 箭头区域
            arrow_rect = QRect(
                option.rect.right() - 17,  # 右边留出17像素的空间给箭头
                option.rect.top(),
                17,
                option.rect.height()
            )
            
            # 绘制下拉按钮背景
            if option.state & QStyle.State_MouseOver:
                # 鼠标悬停时的背景色
                arrow_bg = QColor(230, 230, 230)
            else:
                # 正常状态的背景色
                arrow_bg = QColor(240, 240, 240)
                
            painter.fillRect(arrow_rect, arrow_bg)
            
            # 绘制分隔线
            painter.setPen(QColor(200, 200, 200))
            painter.drawLine(
                arrow_rect.left(),
                arrow_rect.top() + 2,
                arrow_rect.left(),
                arrow_rect.bottom() - 2
            )
            
            # 绘制下拉箭头
            painter.setPen(QColor(120, 120, 120))
            # 三角形顶点
            x = arrow_rect.center().x()
            y_top = arrow_rect.center().y() - 3
            y_bottom = arrow_rect.center().y() + 3
            x_left = x - 3
            x_right = x + 3
            
            arrow_path = QPainterPath()
            arrow_path.moveTo(x_left, y_top)
            arrow_path.lineTo(x_right, y_top)
            arrow_path.lineTo(x, y_bottom)
            arrow_path.lineTo(x_left, y_top)
            
            painter.fillPath(arrow_path, QColor(120, 120, 120))
        
        # 恢复画笔状态
        painter.restore()

class FloatDelegate(QStyledItemDelegate):
    """用于处理显示和编辑浮点数的委托，确保保留2位小数"""
    
    def displayText(self, value, locale):
        """将显示的文本格式化为保留2位小数"""
        try:
            number = float(value)
            return f"{number:.2f}"
        except (ValueError, TypeError):
            # 如果无法转换为浮点数，就返回原始值
            return super().displayText(value, locale)
    
    def setModelData(self, editor, model, index):
        """设置模型数据时格式化为浮点数"""
        try:
            # 获取编辑器的文本
            text = editor.text().strip()
            # 如果为空，则设置为0.00
            if not text:
                model.setData(index, "0.00", Qt.EditRole)
                return
                
            # 尝试转换为浮点数
            value = float(text)
            # 格式化为2位小数并设置
            model.setData(index, f"{value:.2f}", Qt.EditRole)
        except ValueError:
            # 如果无法解析为浮点数，则使用原始值
            super().setModelData(editor, model, index)

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.db = Database() # 在这里实例化时，如果数据库不存在会抛出 ConnectionError
        self.current_user = None  # 当前登录用户信息
        
        # 创建QSettings对象用于保存/加载用户配置
        self.settings = QSettings("UPTEC", "MachiningOrderManagement")
        
        # 创建浮点数委托，用于格式化数字显示
        self.float_delegate = FloatDelegate()
        
        # 创建属性列的下拉框委托
        self.remark_delegate = ComboBoxDelegate(['', 'A产品需求', 'B返修', 'C采购下单'])
        # 创建是否来料列的下拉框委托
        self.material_delegate = ComboBoxDelegate(['', '是', '否', '部分'])
        
        # 显示登录界面
        self.show_login_dialog()
        
        # 初始化界面（只在登录成功后完成）
        self.setWindowTitle("UPTEC机加件订单管理系统")
        self.setGeometry(100, 100, 1200, 800)
    
    def show_login_dialog(self):
        """显示登录对话框"""
        from login import LoginDialog
        login_dialog = LoginDialog()
        login_dialog.login_success.connect(self.on_login_success)
        if login_dialog.exec_() != LoginDialog.Accepted:
            # 用户取消登录，关闭应用程序
            sys.exit()
    
    def on_login_success(self, user):
        """登录成功处理"""
        self.current_user = user
        self.init_ui()
        self.load_data()
        self.set_current_user(user)
        self.show()
    
    def init_ui(self):
        """初始化UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建按钮区域
        button_layout = QHBoxLayout()
        self.import_btn = QPushButton("导入表格")
        self.export_btn = QPushButton("下料导出")
        self.add_row_btn = QPushButton("添加订单")
        self.process_btn = QPushButton("加工完成")
        self.delivery_btn = QPushButton("交付完成")
        self.delete_btn = QPushButton("删除")
        self.log_btn = QPushButton("删除记录查询")
        # 全选复选框
        self.select_all_box = QCheckBox("全选")
        self.select_all_box.stateChanged.connect(self.select_all_changed)
        # 清除筛选按钮
        self.clear_filter_btn = QPushButton("清除筛选")
        self.clear_filter_btn.clicked.connect(self.clear_all_filters)
        # 添加更新数据按钮
        self.refresh_btn = QPushButton("更新数据")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #E3F2FD, stop:1 #BBDEFB);
                border: 1px solid #90CAF9;
                border-radius: 3px;
                min-height: 24px;
                padding: 0 10px;
                color: #1565C0;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #BBDEFB, stop:1 #90CAF9);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #90CAF9, stop:1 #64B5F6);
            }
        """)
        self.refresh_btn.clicked.connect(lambda: self.refresh_data(manual=True))
        # 添加"全部导出"按钮
        self.export_all_btn = QPushButton("全部导出")
        self.export_all_btn.clicked.connect(self.export_all_selected_rows)
        # 添加"返修统计"按钮
        self.repair_stats_btn = QPushButton("返修统计")
        self.repair_stats_btn.clicked.connect(self.show_repairing_stats_dialog)
        
        # 按钮布局顺序调整：在"更新数据"左侧插入"全部导出"
        button_layout.addWidget(self.select_all_box)
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.add_row_btn)
        button_layout.addWidget(self.process_btn)
        button_layout.addWidget(self.delivery_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.log_btn)
        button_layout.addWidget(self.repair_stats_btn) # 添加按钮到布局
        button_layout.addWidget(self.clear_filter_btn)
        button_layout.addWidget(self.export_all_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        
        # 创建状态栏显示上次更新时间
        self.status_bar = QLabel("上次更新: 未更新")
        self.status_bar.setStyleSheet("color: #757575; font-size: 10px;")
        status_layout = QHBoxLayout()
        status_layout.addStretch()
        status_layout.addWidget(self.status_bar)
        
        # 设置列数 - 根据用户角色确定
        self.admin_columns = 27  # 包括额外字段
        self.normal_columns = 23  # 普通用户的列数
        
        # 创建表格
        self.table = QTableWidget()
        # 默认设置最大列数，之后会根据用户角色决定显示多少列
        self.table.setColumnCount(self.admin_columns)
        
        # 设置表头
        self.admin_headers = [
            "", "MO编号", "项目编号", "名称", "图号", "数量", "材质", 
            "下料尺寸", "下料工艺要求", "CNC工时", "CNC编号", "车床时间", "慢丝时间", "快丝时间", 
            "接单日期", "交期日期", "来料需求日期", "是否来料", "加工完成时间", "入库日期", "发货地点", "快递", "属性",
            "预估CNC时间", "预估车床时间", "预估线割时间", "状态"
        ]
        
        self.normal_headers = [
            "", "MO编号", "项目编号", "名称", "图号", "数量", "材质", 
            "下料尺寸", "下料工艺要求", "CNC工时", "CNC编号", "车床时间", "慢丝时间", "快丝时间", 
            "接单日期", "交期日期", "来料需求日期", "是否来料", "加工完成时间", "入库日期", "发货地点", "快递", "属性"
        ]
        
        # 默认先设置为完整表头，后续会根据用户角色调整
        self.table.setHorizontalHeaderLabels(self.admin_headers)
        
        # 设置支持筛选的表头
        filter_header = FilterHeaderView(Qt.Horizontal, self.table)
        self.table.setHorizontalHeader(filter_header)
        
        # 调整表格
        # 将默认列宽设置为可交互调整，而不是自动拉伸
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 复选框列保持固定宽度
        self.table.setColumnWidth(0, 40)  # 设置复选框列宽
        
        # 设置其他列的初始宽度
        default_widths = {
            1: 100,  # MO编号
            2: 100,  # 项目编号
            3: 120,  # 名称
            4: 100,  # 图号
            5: 60,   # 数量
            6: 100,  # 材质
            7: 120,  # 下料尺寸
            8: 150,  # 下料工艺要求
            9: 80,   # CNC工时
            10: 80,  # CNC编号
            11: 80,  # 车床时间
            12: 80,  # 慢丝时间
            13: 80,  # 快丝时间
            14: 100, # 接单日期
            15: 100, # 交期日期
            16: 100, # 来料需求日期
            17: 80,  # 是否来料
            18: 120, # 加工完成时间
            19: 120, # 入库日期
            20: 100, # 发货地点
            21: 80,  # 快递
            22: 120, # 属性
            23: 80,  # 预估CNC时间
            24: 100, # 预估车床时间
            25: 100, # 预估线割时间
            26: 80   # 状态
        }
        
        # 应用初始列宽
        for col, width in default_widths.items():
            if col < self.table.columnCount():
                self.table.setColumnWidth(col, width)
        
        # 启用表格最后一节拉伸填充空白区域
        self.table.horizontalHeader().setStretchLastSection(True)
        
        self.table.setSortingEnabled(False)  # 禁用排序
        
        # 设置自定义委托用于格式化浮点数显示
        self.table.setItemDelegateForColumn(23, self.float_delegate)  # 预估CNC时间
        self.table.setItemDelegateForColumn(24, self.float_delegate)  # 预估车床时间
        self.table.setItemDelegateForColumn(25, self.float_delegate)  # 预估线割时间
        
        # 设置下拉框委托
        self.table.setItemDelegateForColumn(17, self.material_delegate)  # 是否来料
        self.table.setItemDelegateForColumn(22, self.remark_delegate)    # 属性 - 确保使用ComboBoxDelegate
        
        # 添加自动刷新相关控件
        self.auto_refresh_checkbox = QCheckBox("自动刷新")
        self.auto_refresh_checkbox.setChecked(True)
        self.auto_refresh_checkbox.stateChanged.connect(self.toggle_auto_refresh)
        self.auto_refresh_interval = QLineEdit("60")
        self.auto_refresh_interval.setFixedWidth(40)
        self.auto_refresh_interval.setValidator(QIntValidator(10, 3600))
        self.auto_refresh_interval.setToolTip("自动刷新间隔（秒）")
        auto_refresh_layout = QHBoxLayout()
        auto_refresh_layout.addWidget(self.auto_refresh_checkbox)
        auto_refresh_layout.addWidget(QLabel("间隔(s):"))
        auto_refresh_layout.addWidget(self.auto_refresh_interval)
        auto_refresh_layout.addStretch()
        
        # 创建自动刷新定时器，但不启动它
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.stop()  # 确保定时器初始状态是停止的
        
        # 添加布局
        main_layout.addLayout(button_layout)
        main_layout.addLayout(auto_refresh_layout)
        main_layout.addWidget(self.table)
        main_layout.addLayout(status_layout)
        
        # 连接信号和槽
        self.import_btn.clicked.connect(self.import_excel)
        self.export_btn.clicked.connect(self.export_excel)
        self.add_row_btn.clicked.connect(self.add_new_row)
        self.process_btn.clicked.connect(self.mark_processing_complete)
        self.delivery_btn.clicked.connect(self.mark_delivery_complete)
        self.delete_btn.clicked.connect(self.delete_selected_rows)
        self.log_btn.clicked.connect(self.show_delete_logs)
        
        # 设置表格可编辑
        self.table.setEditTriggers(QAbstractItemView.DoubleClicked | QAbstractItemView.EditKeyPressed)
        
        # 连接单元格编辑信号 - 确保初始化时正确连接
        self.table.cellChanged.connect(self.cell_changed)
        
        # 记录上次数据更新的时间
        self.last_update_time = QDateTime.currentDateTime()
        self.update_status_bar()
        
        # 存储当前编辑的行ID，用于检测冲突
        self.editing_records = set()
        
        # 创建汇总行
        self.summary_row = -1  # 标记汇总行的位置
        # 根据当前用户的角色调整UI
        self.adjust_ui_for_user_role()
        
        # 加载保存的列宽设置
        self.load_column_widths()
        # 自动刷新定时器
        self.auto_refresh_timer = QTimer(self)
        self.auto_refresh_timer.timeout.connect(self.refresh_data)
        # 仅当勾选了自动刷新才启动定时器
        if self.auto_refresh_checkbox.isChecked():
            interval_text = self.auto_refresh_interval.text()
            interval = int(interval_text) if interval_text.isdigit() else 60
            self.auto_refresh_timer.start(interval * 1000)
    
    def adjust_ui_for_user_role(self):
        """根据用户角色调整UI"""
        is_admin = self.current_user and self.current_user.get('role') == 'admin'
        
        # 设置表格列数
        if is_admin:
            self.table.setColumnCount(self.admin_columns)
            self.table.setHorizontalHeaderLabels(self.admin_headers)
            
            # 设置自定义委托用于格式化浮点数显示
            self.table.setItemDelegateForColumn(23, self.float_delegate)  # 预估CNC时间
            self.table.setItemDelegateForColumn(24, self.float_delegate)  # 预估车床时间
            self.table.setItemDelegateForColumn(25, self.float_delegate)  # 预估线割时间
        else:
            self.table.setColumnCount(self.normal_columns)
            self.table.setHorizontalHeaderLabels(self.normal_headers)
            
        # 刷新数据以适应新的列数
        self.load_data()
        
        # 重新加载列宽设置
        self.load_column_widths()
    
    def set_current_user(self, user):
        """设置当前用户并更新UI"""
        self.current_user = user
        
        # 更新窗口标题
        username = user.get('username', '未知用户')
        role = '管理员' if user.get('role') == 'admin' else '普通用户'
        self.setWindowTitle(f"UPTEC机加件订单管理系统 - 用户: {username} ({role})")
        
        # 根据用户角色调整UI
        self.adjust_ui_for_user_role()
        
        # 如果不是管理员，禁用一些功能
        if user.get('role') != 'admin':
            # 可以在这里添加对非管理员的功能限制
            pass
    
    def update_status_bar(self):
        """更新状态栏显示上次更新时间"""
        self.status_bar.setText(f"上次更新: {self.last_update_time.toString('yyyy-MM-dd HH:mm:ss')}")
    
    def update_table_with_changes(self, changed_records):
        """使用更改的记录更新表格
        Args:
            changed_records: 包含从数据库获取的更改记录的列表
        """
        if not changed_records:
            return
            
        # 创建ID到行索引的映射，以便快速查找
        id_to_row = {}
        for row in range(self.table.rowCount()):
            item = self.table.item(row, 1)
            if item:
                record_id = item.data(Qt.UserRole)
                if record_id:
                    id_to_row[record_id] = row
        
        for record in changed_records:
            record_id = record['id']
            
            # 检查记录是否已存在于表格中
            if record_id in id_to_row:
                row = id_to_row[record_id]
                # 更新行数据
                self.update_row_data(row, record)
            else:
                # 添加新行
                self.add_new_row_from_record(record)
        
        # 更新上次更新时间
        self.last_update_time = QDateTime.currentDateTime()
        self.update_status_bar()
    
    def update_row_data(self, row, record):
        """使用数据库记录更新表格的特定行
        Args:
            row: 表格中的行索引
            record: 包含记录数据的字典
        """
        # 暂时断开信号连接，以避免触发cellChanged信号
        self.table.cellChanged.disconnect(self.cell_changed)
        
        try:
            # 更新每个单元格
            # 跳过第一列(复选框)
            # ID(第二列)
            item = QTableWidgetItem(str(record.get('mo_number', '')))
            item.setData(Qt.UserRole, record['id'])
            self.table.setItem(row, 1, item)
            
            # 更新其他单元格
            columns_data = [
                (2, 'project_number'), 
                (3, 'name'),
                (4, 'drawing_number'),
                (5, 'quantity'),
                (6, 'material'),
                (7, 'cutting_size'),
                (8, 'cutting_process'),
                (9, 'cnc_hours'),
                (10, 'cnc_number'),
                (11, 'lathe_hours'),
                (12, 'slow_wire_hours'),
                (13, 'fast_wire_hours'),
                (14, 'order_date'),
                (15, 'delivery_date'),
                (16, 'material_required_date'),
                (18, 'processing_completion_time'),
                (19, 'storage_date'),
                (20, 'shipping_location'),
                (21, 'express'),
                (22, 'remark')
            ]
            
            for col, field in columns_data:
                if field in record and record[field] is not None:
                    self.table.setItem(row, col, QTableWidgetItem(str(record[field])))
                else:
                    self.table.setItem(row, col, QTableWidgetItem(""))
            
            # 特殊处理下拉框列
            # 处理是否来料列
            if 'is_material_arrived' in record:
                material_status = record['is_material_arrived']
                material_combobox = QComboBox()
                material_combobox.addItems(["", "是", "否", "部分"])
                
                if material_status in ["", "是", "否", "部分"]:
                    material_combobox.setCurrentText(material_status)
                    
                # 连接信号
                material_combobox.currentTextChanged.connect(
                    lambda text, r=row: self.update_material_status(r, text)
                )
                
                self.table.setCellWidget(row, 17, material_combobox)
                
            # 处理属性列
            if 'remark' in record:
                self.table.setItem(row, 22, QTableWidgetItem(record['remark']))
        
        finally:
            # 重新连接信号
            self.table.cellChanged.connect(self.cell_changed)
    
    def add_new_row_from_record(self, record):
        """从数据库记录添加新行"""
        # 获取当前行数
        current_row_count = self.table.rowCount()
        
        # 计算插入位置 - 如果有汇总行，则在汇总行之前插入
        if self.summary_row >= 0 and self.summary_row < current_row_count:
            insert_row = self.summary_row
        else:
            # 如果没有汇总行，则在当前最后一行之后添加
            insert_row = current_row_count
        
        # 在指定位置插入新行
        self.table.insertRow(insert_row)
        
        # 如果有汇总行，且位置低于插入位置（说明不是先移除再插入的情况），则更新汇总行索引
        if self.summary_row >= 0 and self.summary_row == insert_row:
            self.summary_row += 1
        
        # 添加复选框
        checkbox = QCheckBox()
        checkbox_container = QWidget()
        layout = QHBoxLayout(checkbox_container)
        layout.addWidget(checkbox)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(0, 0, 0, 0)
        self.table.setCellWidget(insert_row, 0, checkbox_container)
        
        # 设置MO编号和记录ID
        mo_item = QTableWidgetItem(str(record.get('mo_number', '')))
        mo_item.setData(Qt.UserRole, record['id'])
        self.table.setItem(insert_row, 1, mo_item)
        
        # 填充其他字段
        for col, field in enumerate([
            'project_number', 'name', 'drawing_number', 'quantity', 'material',
            'cutting_size', 'cutting_process', 'cnc_hours', 'cnc_number',
            'lathe_hours', 'slow_wire_hours', 'fast_wire_hours', 'order_date',
            'delivery_date', 'material_required_date', 'is_material_arrived',
            'processing_completion_time', 'storage_date', 'shipping_location',
            'express', 'remark'
        ], start=2):
            value = record.get(field, '')
            self.table.setItem(insert_row, col, QTableWidgetItem(str(value) if value is not None else ''))
        
        # 如果是管理员，设置额外的4个字段
        if self.current_user and self.current_user.get('role') == 'admin':
            # 预估CNC时间
            est_cnc_hours = record.get('est_cnc_hours', 0)
            try:
                est_cnc_hours = float(est_cnc_hours) if est_cnc_hours is not None else 0.0
                self.table.setItem(insert_row, 23, QTableWidgetItem(f"{est_cnc_hours:.2f}"))
            except (ValueError, TypeError):
                self.table.setItem(insert_row, 23, QTableWidgetItem("0.00"))
            
            # 预估车床时间
            est_lathe_hours = record.get('est_lathe_hours', 0)
            try:
                est_lathe_hours = float(est_lathe_hours) if est_lathe_hours is not None else 0.0
                self.table.setItem(insert_row, 24, QTableWidgetItem(f"{est_lathe_hours:.2f}"))
            except (ValueError, TypeError):
                self.table.setItem(insert_row, 24, QTableWidgetItem("0.00"))
            
            # 预估线割时间
            est_wire_hours = record.get('est_wire_hours', 0)
            try:
                est_wire_hours = float(est_wire_hours) if est_wire_hours is not None else 0.0
                self.table.setItem(insert_row, 25, QTableWidgetItem(f"{est_wire_hours:.2f}"))
            except (ValueError, TypeError):
                self.table.setItem(insert_row, 25, QTableWidgetItem("0.00"))
            
            # 状态下拉框
            status_combo = QComboBox()
            status_combo.addItems(["待加工", "已完成", ""])
            status_value = record.get('status', '')
            if status_value:
                status_combo.setCurrentText(status_value)
            self.table.setCellWidget(insert_row, 26, status_combo)
            status_combo.currentTextChanged.connect(lambda text, r=insert_row: self.update_status(r, text))
            
            # 更新汇总时间
            self.update_time_summary()
        
        # 如果是来料状态列，添加下拉框
        material_combo = QComboBox()
        material_combo.addItems(["", "是", "否", "部分"])
        material_status = record.get('is_material_arrived', '')
        if material_status:
            material_combo.setCurrentText(material_status)
        self.table.setCellWidget(insert_row, 17, material_combo)
        material_combo.currentTextChanged.connect(lambda text, r=insert_row: self.update_material_status(r, text))
        
        # 属性列添加下拉框
        remark_combo = QComboBox()
        remark_combo.addItems(["", "A产品需求", "B返修", "C采购下单"])
        remark_status = record.get('remark', '')
        if remark_status:
            remark_combo.setCurrentText(remark_status)
        self.table.setCellWidget(insert_row, 22, remark_combo)
        remark_combo.currentTextChanged.connect(lambda text, r=insert_row: self.update_remark_status(r, text))
        
        return insert_row
    
    def refresh_data(self, manual=False):
        try:
            old_orders = getattr(self, '_last_orders', None)
            if old_orders is None:
                old_orders = []
            old_id_set = set([o['id'] for o in old_orders]) if old_orders else set()
            old_map = {o['id']: o for o in old_orders} if old_orders else {}

            new_orders = self.db.get_all_orders()
            new_id_set = set([o['id'] for o in new_orders])
            new_map = {o['id']: o for o in new_orders}

            updated_count = 0
            updated_count += len(new_id_set - old_id_set)
            updated_count += len(old_id_set - new_id_set)
            for cid in (old_id_set & new_id_set):
                old_row = old_map[cid]
                new_row = new_map[cid]
                if any(str(old_row.get(k, '')) != str(new_row.get(k, '')) for k in new_row.keys()):
                    updated_count += 1

            self._last_orders = new_orders
            self.load_data()

            # 自动刷新时状态栏提示 - 保留
            if hasattr(self, 'status_bar'):
                # 只在自动刷新时更新状态栏（manual=False）
                if not manual:
                    self.status_bar.setText(f"上次自动刷新: {QDateTime.currentDateTime().toString('yyyy-MM-dd HH:mm:ss')}")
                else:
                    # 手动刷新时更新为"手动更新"时间
                    self.last_update_time = QDateTime.currentDateTime()
                    self.update_status_bar() # 调用现有方法更新状态栏为"上次更新"

        except Exception as e:
            print(f"刷新数据错误: {e}")
            # 保留错误弹窗
            QMessageBox.warning(self, "刷新数据警告", f"刷新数据时发生错误: {str(e)}")
            # 更新状态栏为通用更新时间
            self.last_update_time = QDateTime.currentDateTime()
            self.update_status_bar()

    def save_column_widths(self):
        """保存表格列宽到用户配置"""
        if not self.table:
            return
            
        # 保存主表格列宽
        for col in range(self.table.columnCount()):
            width = self.table.columnWidth(col)
            self.settings.setValue(f"MainTable/col{col}_width", width)
            
        # 保存窗口大小
        self.settings.setValue("MainWindow/size", self.size())
        self.settings.setValue("MainWindow/pos", self.pos())
        
    def load_column_widths(self):
        """从用户配置加载表格列宽"""
        if not self.table:
            return
            
        # 加载主表格列宽
        for col in range(self.table.columnCount()):
            width_key = f"MainTable/col{col}_width"
            if self.settings.contains(width_key):
                width = self.settings.value(width_key, type=int)
                if width > 0:  # 确保宽度合理
                    self.table.setColumnWidth(col, width)
        
        # 加载窗口大小和位置
        if self.settings.contains("MainWindow/size"):
            size = self.settings.value("MainWindow/size")
            self.resize(size)
        
        if self.settings.contains("MainWindow/pos"):
            pos = self.settings.value("MainWindow/pos")
            self.move(pos)
    
    def closeEvent(self, event):
        """重写关闭事件，保存列宽和窗口大小"""
        self.save_column_widths()
        super().closeEvent(event)

    def toggle_auto_refresh(self, state):
        if state == Qt.Checked:
            interval = int(self.auto_refresh_interval.text())
            self.auto_refresh_timer.start(interval * 1000)
        else:
            self.auto_refresh_timer.stop()

    def cell_changed(self, row, column):
        try:
            # 排除不需要处理的行
            if column == 0 or row == self.summary_row:
                return
            id_item = self.table.item(row, 1)
            if not id_item:
                return
            record_id = id_item.data(Qt.UserRole)
            if not record_id:
                return
            item = self.table.item(row, column)
            if not item:
                return
            new_value = item.text()
            if not new_value:
                new_value = None
            field_mapping = {
                1: ('mo_number', str),
                2: ('project_number', str),
                3: ('name', str),
                4: ('drawing_number', str),
                5: ('quantity', int),
                6: ('material', str),
                7: ('cutting_size', str),
                8: ('cutting_process', str),
                9: ('cnc_hours', float),
                10: ('cnc_number', str),
                11: ('lathe_hours', float),
                12: ('slow_wire_hours', float),
                13: ('fast_wire_hours', float),
                14: ('order_date', str),
                15: ('delivery_date', str),
                16: ('material_required_date', str),
                18: ('processing_completion_time', str),
                19: ('storage_date', str),
                20: ('shipping_location', str),
                21: ('express', str),
                22: ('remark', str),
                23: ('est_cnc_hours', float),
                24: ('est_lathe_hours', float),
                25: ('est_wire_hours', float)
            }
            # 编辑冲突检测
            # 1. 先查数据库当前update_time
            from database import Database
            db = Database()
            db_record = db.get_order_by_id(record_id)
            db_update_time = db_record.get('update_time') if db_record else None
            # 2. 再查当前表格该行的update_time
            table_update_time = None
            update_time_col = None
            # 查找update_time列索引
            for col in range(self.table.columnCount()):
                header = self.table.horizontalHeaderItem(col)
                if header and ('更新时间' in header.text() or header.text() == 'update_time'):
                    update_time_col = col
                    break
            if update_time_col is not None:
                ut_item = self.table.item(row, update_time_col)
                if ut_item:
                    table_update_time = ut_item.text()
            # 3. 比较
            if db_update_time and table_update_time and str(db_update_time) != str(table_update_time):
                QMessageBox.warning(self, "编辑冲突", "该数据已被他人修改，请刷新后再编辑！")
                # 恢复为数据库值
                if column in field_mapping:
                    field_name, _ = field_mapping[column]
                    item.setText(str(db_record.get(field_name, '')))
                return
            # ...原有cell_changed逻辑...
            if column in field_mapping:
                field_name, convert_func = field_mapping[column]
                if new_value is not None:
                    try:
                        if convert_func == int:
                            new_value = int(new_value)
                        elif convert_func == float:
                            new_value = float(new_value)
                            if column in [23, 24, 25]:
                                formatted_value = f"{new_value:.2f}"
                                self.table.blockSignals(True)
                                item.setText(formatted_value)
                                self.table.blockSignals(False)
                    except ValueError:
                        pass
                self.db.update_order(record_id, field_name, new_value)
                is_time_column = column in [23, 24, 25]
                is_admin = self.current_user and self.current_user.get('role') == 'admin'
                if is_time_column and is_admin:
                    self.update_time_summary()
                self.editing_records.add(record_id)
                self.editing_records.discard(record_id)
                self.update_filter_for_column(column, row, new_value)
        except Exception as e:
            print(f"单元格修改错误 {e}")

    def update_filter_for_column(self, column, row, new_value):
        """更新列的筛选值"""
        header = self.table.horizontalHeader()
        if isinstance(header, FilterHeaderView) and column in header.all_column_values:
            # 如果该列已有筛选值集合，则更新它
            header.all_column_values[column] = header.collect_all_values(self.table, column)
            
            # 如果该列有筛选条件，需要确保新值被包含在筛选条件中
            if column in header.filters:
                # 获取新值的显示文本
                new_display_value = str(new_value) if new_value is not None else ""
                
                # 如果是非空新值，将其添加到筛选条件中
                if new_display_value:
                    header.filters[column].add(new_display_value)
                    # 重新应用筛选条件
                    header.apply_filters(self.table)
                    
                    # 更新按钮状态
                    if column in header.filter_buttons:
                        button = header.filter_buttons[column]
                        unique_values = header.all_column_values[column]['values']
                        if len(header.filters[column]) < len(unique_values):
                            button.set_active(True)
                        else:
                            button.set_active(False)

    def update_status(self, row, text):
        """更新状态"""
        record_id = self.table.item(row, 1).data(Qt.UserRole)
        self.db.update_order(record_id, 'status', text)
        
        # 添加到编辑中的记录集，然后移除（表示已完成编辑）
        self.editing_records.add(record_id)
        self.editing_records.discard(record_id)

    def update_remark_status(self, row, text):
        """更新属性状态"""
        try:
            # 获取记录ID
            id_item = self.table.item(row, 1)
            if not id_item:
                return
            
            record_id = id_item.data(Qt.UserRole)
            if not record_id:
                return
            
            # 更新数据库
            self.db.update_record(record_id, {'remark': text})
            
            # 将记录标记为已编辑
            self.editing_records.add(record_id)
            self.editing_records.discard(record_id)
        except Exception as e:
            print(f"更新属性状态错误: {e}")

    def update_time_summary(self):
        """更新时间汇总"""
        if not self.current_user or self.current_user.get('role') != 'admin':
            return
        
        # 计算当前表格中所有可见行的预估时间总和
        total_est_cnc_hours = 0.0
        total_est_lathe_hours = 0.0
        total_est_wire_hours = 0.0
        
        try:
            # 先确认汇总行索引是否有效
            if self.summary_row >= self.table.rowCount() or self.summary_row < 0:
                self.summary_row = -1
            
            # 遍历所有非汇总行
            for row in range(self.table.rowCount()):
                # 跳过汇总行和隐藏的行
                if row == self.summary_row or self.table.isRowHidden(row):
                    continue
                
                # 预估CNC时间
                est_cnc_item = self.table.item(row, 23)
                if est_cnc_item:
                    try:
                        total_est_cnc_hours += float(est_cnc_item.text() or "0")
                    except (ValueError, TypeError):
                        pass
                
                # 预估车床时间
                est_lathe_item = self.table.item(row, 24)
                if est_lathe_item:
                    try:
                        total_est_lathe_hours += float(est_lathe_item.text() or "0")
                    except (ValueError, TypeError):
                        pass
                
                # 预估线割时间
                est_wire_item = self.table.item(row, 25)
                if est_wire_item:
                    try:
                        total_est_wire_hours += float(est_wire_item.text() or "0")
                    except (ValueError, TypeError):
                        pass
            
            # 更新汇总行 - 只有当表格有实际数据行时才添加汇总行
            if self.table.rowCount() > 0:
                # 确保汇总行总是在最后一行
                self.add_summary_row(total_est_cnc_hours, total_est_lathe_hours, total_est_wire_hours)
        
        except Exception as e:
            print(f"更新汇总时间发生错误: {e}")

    def update_material_status(self, row, text):
        """更新是否来料状态"""
        record_id = self.table.item(row, 1).data(Qt.UserRole)
        self.db.update_order(record_id, 'is_material_arrived', text)
        
        # 添加到编辑中的记录集，然后移除（表示已完成编辑）
        self.editing_records.add(record_id)
        self.editing_records.discard(record_id)

    def add_summary_row(self, total_est_cnc_hours, total_est_lathe_hours, total_est_wire_hours):
        """添加汇总行"""
        # 阻止表格信号触发
        self.table.blockSignals(True)
        
        try:
            # 如果已经有汇总行，先移除
            if self.summary_row >= 0 and self.summary_row < self.table.rowCount():
                self.table.removeRow(self.summary_row)
                self.summary_row = -1
            
            # 确保在添加汇总行前表格不为空
            if self.table.rowCount() == 0:
                self.table.blockSignals(False)
                return
            
            # 在表格末尾添加一行
            row_count = self.table.rowCount()
            self.table.insertRow(row_count)
            self.summary_row = row_count
            
            # 设置汇总行样式和内容
            summary_font = QFont()
            summary_font.setBold(True)
            
            # 设置汇总标题
            summary_item = QTableWidgetItem("汇总")
            summary_item.setFont(summary_font)
            summary_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_count, 22, summary_item)  # 备注列显示汇总
            
            # 设置预估CNC时间汇总
            est_cnc_sum = QTableWidgetItem(f"{total_est_cnc_hours:.2f}")
            est_cnc_sum.setFont(summary_font)
            est_cnc_sum.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_count, 23, est_cnc_sum)
            
            # 设置预估车床时间汇总
            est_lathe_sum = QTableWidgetItem(f"{total_est_lathe_hours:.2f}")
            est_lathe_sum.setFont(summary_font)
            est_lathe_sum.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_count, 24, est_lathe_sum)
            
            # 设置预估线割时间汇总
            est_wire_sum = QTableWidgetItem(f"{total_est_wire_hours:.2f}")
            est_wire_sum.setFont(summary_font)
            est_wire_sum.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_count, 25, est_wire_sum)
            
            # 在下拉框列（17:是否来料，26:状态）创建普通单元格
            empty_item1 = QTableWidgetItem("")
            empty_item1.setBackground(QColor(240, 240, 240))
            self.table.setItem(row_count, 17, empty_item1)
            
            if self.current_user and self.current_user.get('role') == 'admin':
                empty_item2 = QTableWidgetItem("")
                empty_item2.setBackground(QColor(240, 240, 240))
                self.table.setItem(row_count, 26, empty_item2)
            
            # 设置汇总行背景颜色
            for col in range(self.table.columnCount()):
                item = self.table.item(row_count, col)
                if item:
                    item.setBackground(QColor(240, 240, 240))  # 浅灰色背景
        finally:
            # 恢复表格信号
            self.table.blockSignals(False)

    def select_all_changed(self, state):
        """全选复选框状态改变事件"""
        for row in range(self.table.rowCount()):
            # 跳过汇总行
            if row == self.summary_row:
                continue
            
            checkbox_container = self.table.cellWidget(row, 0)
            if checkbox_container:
                # 通过容器查找复选框
                checkbox = checkbox_container.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(state == Qt.Checked)
    
    def clear_all_filters(self):
        """清除所有筛选"""
        header = self.table.horizontalHeader()
        if isinstance(header, FilterHeaderView):
            header.clear_filters()
            
            # 更新时间汇总（如果是管理员）
            if self.current_user and self.current_user.get('role') == 'admin':
                self.update_time_summary()
    
    def import_excel(self):
        """导入Excel文件"""
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel Files (*.xlsx *.xls)"
        )
        
        if file_path:
            try:
                # 创建Excel导入器实例
                importer = ExcelImporter(file_path)
                
                # 导入数据
                data = importer.import_from_file()
                
                if data:
                    # 将数据保存到数据库
                    imported_count = 0
                    for order in data:
                        result = self.db.add_order(order)
                        if result:
                            imported_count += 1
                    
                    # 完全重新加载表格数据
                    self.table.clearContents()
                    self.table.setRowCount(0)
                    self.summary_row = -1  # 重置汇总行索引
                    
                    # 重新加载数据
                    self.load_data()
                    
                    QMessageBox.information(self, "成功", f"Excel数据导入成功，新增了{imported_count}条记录")
                else:
                    QMessageBox.warning(self, "警告", "没有找到有效数据")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入失败：{str(e)}")
                print(f"导入Excel错误: {e}")
    
    def export_excel(self):
        """导出选中行到Excel文件"""
        # 获取选中的行
        selected_rows = []
        for row in range(self.table.rowCount()):
            # 跳过汇总行
            if row == self.summary_row:
                continue
                
            checkbox_container = self.table.cellWidget(row, 0)
            if checkbox_container:
                # 通过容器查找复选框
                checkbox = checkbox_container.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_rows.append(row)
        
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请选择要导出的行！")
            return
        
        # 要导出的列索引和标题
        columns_to_export = [
            (3, "MO编号"),
            (2, "项目编号"),
            (3, "名称"),
            (4, "图号"),
            (5, "数量"),
            (6, "材质"),
            (7, "下料尺寸"),
            (8, "下料工艺要求")
        ]
        
        # 收集选中行的数据
        export_data = []
        for row in selected_rows:
            row_data = {}
            for col_idx, col_title in columns_to_export:
                item = self.table.item(row, col_idx)
                if item:
                    row_data[col_title] = item.text()
                else:
                    row_data[col_title] = ""
            export_data.append(row_data)
        
        if not export_data:
            QMessageBox.warning(self, "警告", "没有找到可导出的数据！")
            return
        
        # 选择保存文件路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出Excel",
            "",
            "Excel Files (*.xlsx)"
        )
        
        if not file_path:
            return  # 用户取消了保存
            
        # 确保文件扩展名正确
        if not file_path.endswith('.xlsx'):
            file_path += '.xlsx'
            
        try:
            # 导出到Excel
            import pandas as pd
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 导出到Excel
            df.to_excel(file_path, index=False)
            
            QMessageBox.information(self, "成功", f"已成功导出 {len(export_data)} 行数据到 {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出Excel失败: {str(e)}")
            print(f"导出Excel错误: {e}")
    
    def mark_processing_complete(self):
        """标记选中的订单为加工完成"""
        try:
            # 遍历表格找到选中的行
            selected_count = 0
            for row in range(self.table.rowCount()):
                # 跳过汇总行
                if row == self.summary_row:
                    continue
                    
                checkbox_container = self.table.cellWidget(row, 0)
                if checkbox_container:
                    # 通过容器查找复选框
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        selected_count += 1
                        # 获取记录ID
                        record_id = self.table.item(row, 1).data(Qt.UserRole)
                        
                        # 打印调试信息
                        print(f"更新记录 ID: {record_id}, 状态: 已完成") # 移除时间信息
                        
                        # 更新状态为"已完成" - 使用数据库定义的ENUM值
                        status_result = self.db.update_order(record_id, 'status', '已完成')
                        
                        print(f"更新结果 - 状态: {status_result}") # 移除时间结果
                        
                        # 更新状态列显示
                        if self.current_user and self.current_user.get('role') == 'admin':
                            status_combo = self.table.cellWidget(row, 26)
                            if status_combo:
                                status_combo.setCurrentText('已完成')
                        
                        # 取消选中状态
                        checkbox.setChecked(False)
                        
                        # 添加到编辑中的记录集，然后移除（表示已完成编辑）
                        self.editing_records.add(record_id)
                        self.editing_records.discard(record_id)
            
            # 更新全选复选框状态
            self.select_all_box.setChecked(False)
            
            if selected_count > 0:
                QMessageBox.information(self, "成功", "已更新选中订单的状态！") # 修改提示信息
            else:
                QMessageBox.warning(self, "警告", "请选择要标记的行！")
        except Exception as e:
            print(f"标记加工完成错误: {e}")
            QMessageBox.critical(self, "错误", f"标记加工完成时发生错误 {str(e)}")
    
    def mark_delivery_complete(self):
        """标记选中的订单为交付完成"""
        try:
            # 获取当前日期时间
            current_datetime = QDateTime.currentDateTime().toString('yyyy-MM-dd HH:mm:ss')
            
            # 遍历表格找到选中的行
            selected_count = 0
            for row in range(self.table.rowCount()):
                # 跳过汇总行
                if row == self.summary_row:
                    continue
                    
                checkbox_container = self.table.cellWidget(row, 0)
                if checkbox_container:
                    # 通过容器查找复选框
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        selected_count += 1
                        # 获取记录ID
                        record_id = self.table.item(row, 1).data(Qt.UserRole)
                        
                        # 更新入库日期
                        self.db.update_order(record_id, 'storage_date', current_datetime)
                        
                        # 更新表格显示
                        self.table.setItem(row, 19, QTableWidgetItem(current_datetime))
                        
                        # 取消选中状态
                        checkbox.setChecked(False)
            
            # 更新全选复选框状态
            self.select_all_box.setChecked(False)
            
            if selected_count > 0:
                QMessageBox.information(self, "成功", "已更新选中订单的入库日期！")
            else:
                QMessageBox.warning(self, "警告", "请选择要标记的行！")
        except Exception as e:
            print(f"标记交付完成错误: {e}")
            QMessageBox.critical(self, "错误", f"标记交付完成时发生错误 {str(e)}")
    
    def show_delete_logs(self):
        """显示删除日志对话框"""
        dialog = DeleteLogDialog(self)
        dialog.exec_()
    
    def add_new_row(self):
        """异步添加新行，避免主线程卡顿"""
        self.add_thread = AddOrderThread()
        self.add_thread.finished.connect(self.on_add_order_finished)
        self.add_thread.start()

    def on_add_order_finished(self, record):
        if record:
            self.add_new_row_from_record(record)
            # 如果全选按钮已选中，新行的复选框也应选中
            if hasattr(self, 'select_all_box') and self.select_all_box.isChecked():
                row = self.table.rowCount() - 1
                checkbox = self.table.cellWidget(row, 0)
                if checkbox:
                    checkbox.setChecked(True)
    
    def delete_selected_rows(self):
        """删除选中的行"""
        # 权限检查：只有管理员才能删除
        if not self.current_user or self.current_user.get('role') != 'admin':
            QMessageBox.warning(self, "权限不足", "您无权删除订单，请更换为管理员账户。")
            return
            
        selected_rows = []
        for row in range(self.table.rowCount()):
            # 跳过汇总行
            if row == self.summary_row:
                continue
                
            checkbox_container = self.table.cellWidget(row, 0)
            if checkbox_container:
                # 通过容器查找复选框
                checkbox = checkbox_container.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    # 获取记录ID
                    item = self.table.item(row, 1)
                    if item:
                        record_id = item.data(Qt.UserRole)
                        if record_id:
                            selected_rows.append((row, record_id))
        
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请选择要删除的行！")
            return
        
        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认",
            f"确定要删除选中的{len(selected_rows)}行数据吗?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 获取当前用户名，如果未登录则使用"system"
            username = self.current_user['username'] if self.current_user else "system"
            
            # 如果存在汇总行，先删除它
            if self.summary_row >= 0 and self.summary_row < self.table.rowCount():
                self.table.removeRow(self.summary_row)
                self.summary_row = -1
            
            # 收集所有要删除的记录ID
            record_ids = [record_id for _, record_id in selected_rows]
            
            # 一次性删除所有记录
            if record_ids:
                # 调用带用户名参数的删除方法
                delete_result = self.db.delete_orders(record_ids, username)
                
                if delete_result:
                    # 从后往前删除表格中的行，以避免索引变化的问题
                    for row, _ in sorted(selected_rows, reverse=True):
                        self.table.removeRow(row)
                    
                    # 更新全选复选框状态
                    self.select_all_box.setChecked(False)
                    
                    # 更新汇总行
                    if self.current_user and self.current_user.get('role') == 'admin':
                        self.update_time_summary()
                    
                    QMessageBox.information(self, "成功", "删除成功")
                else:
                    QMessageBox.warning(self, "警告", "删除操作失败，请检查日志")
            else:
                QMessageBox.warning(self, "警告", "没有有效的记录ID可以删除")
    
    def load_data(self):
        """从数据库加载数据到表格"""
        try:
            self.table.setUpdatesEnabled(False)
            self.table.blockSignals(True)
            # 重置全选复选框
            if hasattr(self, 'select_all_box'):
                self.select_all_box.setChecked(False)

            # 1. 彻底清空表格，包括所有 cellWidget 和汇总行
            self.table.setRowCount(0)
            self.summary_row = -1

            # 2. 获取所有订单记录
            orders = self.db.get_all_orders()
            print("数据库返回的订单数量：", len(orders))
            for order in orders:
                print(order)
            if not orders:
                QMessageBox.information(self, "提示", "数据库中没有记录")
                return

            # 3. 设置表格行数
            self.table.setRowCount(len(orders))

            # 检查是否是管理员
            is_admin = self.current_user and self.current_user.get('role') == 'admin'

            # 累计预估时间
            total_est_cnc_hours = 0.0
            total_est_lathe_hours = 0.0
            total_est_wire_hours = 0.0

            # 4. 填充每一行
            for row, order in enumerate(orders):
                # 第一列放置复选框
                checkbox = QCheckBox()
                checkbox_container = QWidget()
                layout = QHBoxLayout(checkbox_container)
                layout.addWidget(checkbox)
                layout.setAlignment(Qt.AlignCenter)
                layout.setContentsMargins(0, 0, 0, 0)
                self.table.setCellWidget(row, 0, checkbox_container)

                # 设置每一列的数据，同时在MO编号列存储record_id作为用户数据
                mo_item = QTableWidgetItem(str(order.get('mo_number', '')))
                mo_item.setData(Qt.UserRole, order['id'])
                self.table.setItem(row, 1, mo_item)

                def get_safe_value(field_name):
                    return str(order.get(field_name, '')) if order.get(field_name) is not None else ""

                self.table.setItem(row, 2, QTableWidgetItem(get_safe_value('project_number')))
                self.table.setItem(row, 3, QTableWidgetItem(get_safe_value('name')))
                self.table.setItem(row, 4, QTableWidgetItem(get_safe_value('drawing_number')))
                self.table.setItem(row, 5, QTableWidgetItem(get_safe_value('quantity')))
                self.table.setItem(row, 6, QTableWidgetItem(get_safe_value('material')))
                self.table.setItem(row, 7, QTableWidgetItem(get_safe_value('cutting_size')))
                self.table.setItem(row, 8, QTableWidgetItem(get_safe_value('cutting_process')))
                self.table.setItem(row, 9, QTableWidgetItem(get_safe_value('cnc_hours')))
                self.table.setItem(row, 10, QTableWidgetItem(get_safe_value('cnc_number')))
                self.table.setItem(row, 11, QTableWidgetItem(get_safe_value('lathe_hours')))
                self.table.setItem(row, 12, QTableWidgetItem(get_safe_value('slow_wire_hours')))
                self.table.setItem(row, 13, QTableWidgetItem(get_safe_value('fast_wire_hours')))
                self.table.setItem(row, 14, QTableWidgetItem(get_safe_value('order_date')))
                self.table.setItem(row, 15, QTableWidgetItem(get_safe_value('delivery_date')))
                self.table.setItem(row, 16, QTableWidgetItem(get_safe_value('material_required_date')))

                # 是否来料下拉框
                material_status = order.get('is_material_arrived', '')
                material_combo = QComboBox()
                material_combo.addItems(["", "是", "否", "部分"])
                if material_status:
                    material_combo.setCurrentText(material_status)
                self.table.setCellWidget(row, 17, material_combo)
                material_combo.currentTextChanged.connect(lambda text, r=row: self.update_material_status(r, text))

                self.table.setItem(row, 18, QTableWidgetItem(get_safe_value('processing_completion_time')))
                self.table.setItem(row, 19, QTableWidgetItem(get_safe_value('storage_date')))
                self.table.setItem(row, 20, QTableWidgetItem(get_safe_value('shipping_location')))
                self.table.setItem(row, 21, QTableWidgetItem(get_safe_value('express')))

                # 属性下拉框
                remark_status = order.get('remark', '')
                remark_combo = QComboBox()
                remark_combo.addItems(["", "A产品需求", "B返修", "C采购下单"])
                if remark_status:
                    remark_combo.setCurrentText(remark_status)
                self.table.setCellWidget(row, 22, remark_combo)
                remark_combo.currentTextChanged.connect(lambda text, r=row: self.update_remark_status(r, text))

                # 如果是管理员，显示额外的字段
                if is_admin:
                    est_cnc_hours = order.get('est_cnc_hours', 0)
                    try:
                        est_cnc_hours = float(est_cnc_hours) if est_cnc_hours is not None else 0.0
                        total_est_cnc_hours += est_cnc_hours
                        est_cnc_item = QTableWidgetItem(f"{est_cnc_hours:.2f}")
                        self.table.setItem(row, 23, est_cnc_item)
                    except (ValueError, TypeError):
                        self.table.setItem(row, 23, QTableWidgetItem("0.00"))

                    est_lathe_hours = order.get('est_lathe_hours', 0)
                    try:
                        est_lathe_hours = float(est_lathe_hours) if est_lathe_hours is not None else 0.0
                        total_est_lathe_hours += est_lathe_hours
                        est_lathe_item = QTableWidgetItem(f"{est_lathe_hours:.2f}")
                        self.table.setItem(row, 24, est_lathe_item)
                    except (ValueError, TypeError):
                        self.table.setItem(row, 24, QTableWidgetItem("0.00"))

                    est_wire_hours = order.get('est_wire_hours', 0)
                    try:
                        est_wire_hours = float(est_wire_hours) if est_wire_hours is not None else 0.0
                        total_est_wire_hours += est_wire_hours
                        est_wire_item = QTableWidgetItem(f"{est_wire_hours:.2f}")
                        self.table.setItem(row, 25, est_wire_item)
                    except (ValueError, TypeError):
                        self.table.setItem(row, 25, QTableWidgetItem("0.00"))

                    status_combo = QComboBox()
                    status_combo.addItems(["待加工", "已完成", ""])
                    status_value = order.get('status', '')
                    if status_value:
                        status_combo.setCurrentText(status_value)
                    self.table.setCellWidget(row, 26, status_combo)
                    status_combo.currentTextChanged.connect(lambda text, r=row: self.update_status(r, text))

            # 5. 添加汇总行（如果需要）
            if is_admin and len(orders) > 0:
                self.add_summary_row(total_est_cnc_hours, total_est_lathe_hours, total_est_wire_hours)

            # 6. 确保所有行都可见
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)

            # 7. 更新过滤按钮
            self.table.horizontalHeader().update_filter_buttons()

            # 8. 更新时间
            self.last_update_time = QDateTime.currentDateTime()
            self.update_status_bar()

            # 9. 重新连接单元格编辑信号
            self.table.cellChanged.connect(self.cell_changed)

        except Exception as e:
            print(f"加载数据错误: {e}")
            try:
                self.table.cellChanged.connect(self.cell_changed)
            except Exception:
                pass
            QMessageBox.critical(self, "错误", f"加载数据时发生错误 {str(e)}")
        finally:
            self.table.blockSignals(False)
            self.table.setUpdatesEnabled(True)

    def export_all_selected_rows(self):
        """导出所有勾选行的所有列内容为Excel"""
        # 获取选中的行
        selected_rows = []
        for row in range(self.table.rowCount()):
            if hasattr(self, 'summary_row') and row == self.summary_row:
                continue
            checkbox_container = self.table.cellWidget(row, 0)
            if checkbox_container:
                checkbox = checkbox_container.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_rows.append(row)
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请选择要导出的行！")
            return
        # 获取所有表头
        headers = []
        for col in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(col)
            if header_item:
                headers.append(header_item.text())
            else:
                headers.append("")
        # 收集选中行的所有列数据
        export_data = []
        for row in selected_rows:
            row_data = {}
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item:
                    row_data[headers[col]] = item.text()
                else:
                    # 如果是下拉框等控件
                    widget = self.table.cellWidget(row, col)
                    if isinstance(widget, QComboBox):
                        row_data[headers[col]] = widget.currentText()
                    else:
                        row_data[headers[col]] = ""
            export_data.append(row_data)
        if not export_data:
            QMessageBox.warning(self, "警告", "没有找到可导出的数据！")
            return
        # 选择保存文件路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出Excel",
            "",
            "Excel Files (*.xlsx)"
        )
        if not file_path:
            return  # 用户取消了保存
        if not file_path.endswith('.xlsx'):
            file_path += '.xlsx'
        try:
            import pandas as pd
            df = pd.DataFrame(export_data)
            df.to_excel(file_path, index=False)
            QMessageBox.information(self, "成功", f"已成功导出 {len(export_data)} 行数据到 {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出Excel失败: {str(e)}")
            print(f"导出Excel错误: {e}")

    def show_repairing_stats_dialog(self):
        """显示返修统计对话框"""
        try:
            from Repairing import RepairingStatsDialog # 确保导入在函数内部，避免循环导入
            # 检查 RepairingStatsDialog 是否成功导入
            if RepairingStatsDialog is None:
                QMessageBox.critical(self, "错误", "无法加载返修统计模块 (Repairing.py)。")
                return
                
            dialog = RepairingStatsDialog(self) # 传入 self 作为父窗口
            dialog.exec_()
        except ImportError as e:
            QMessageBox.critical(self, "导入错误", f"无法导入返修统计模块: {e}\n请确保 Repairing.py 文件存在且无误。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开返修统计窗口时出错: {str(e)}")
            print(f"打开返修统计窗口错误: {e}")

class AddOrderThread(QThread):
    finished = pyqtSignal(dict)
    def __init__(self):
        super().__init__()
    def run(self):
        from database import Database
        db = Database()
        record_id = db.create_empty_order()
        record = db.get_order_by_id(record_id)
        db.close()
        self.finished.emit(record)
