(['D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\main.py'],
 ['D:\\桌面\\develop\\机加件订单管理系统\\maching manage'],
 ['PyQt5.QtWidgets', 'PyQt5.QtCore', 'PyQt5.QtGui'],
 [('C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'scipy',
  'PIL',
  'PyQt5.QtNetwork',
  'PyQt5.QtQml',
  'PyQt5.QtQuick',
  'PyQt5.QtSql',
  'PyQt5.QtTest',
  'PyQt5.QtWebKit',
  'PyQt5.QtWebKitWidgets',
  'PyQt5.QtXml',
  'PyQt5.QtXmlPatterns',
  'PyQt5.QtMultimedia',
  'PyQt5.QtMultimediaWidgets',
  'PyQt5.QtOpenGL',
  'PyQt5.QtPositioning',
  'PyQt5.QtPrintSupport',
  'PyQt5.QtSensors',
  'PyQt5.QtSerialPort',
  'PyQt5.QtWebChannel',
  'PyQt5.QtWebSockets',
  'PyQt5.QtWinExtras',
  'PyQt5.QtX11Extras',
  'PyQt5.QtDBus',
  'PyQt5.QtDesigner',
  'PyQt5.QtHelp',
  'PyQt5.QtLocation',
  'PyQt5.QtNfc',
  'PyQt5.QtScript',
  'PyQt5.QtScriptTools',
  'PyQt5.QtSvg',
  'PyQt5.QtWebEngine',
  'PyQt5.QtWebEngineCore',
  'PyQt5.QtWebEngineWidgets',
  'PyQt5.QtWidgets.QGraphicsEffect',
  'PyQt5.QtWidgets.QGraphicsScene',
  'PyQt5.QtWidgets.QGraphicsView',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('Repairing.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\Repairing.py',
   'DATA'),
  ('config.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\config.py',
   'DATA'),
  ('database.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\database.py',
   'DATA'),
  ('excel_importer.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\excel_importer.py',
   'DATA'),
  ('login.py', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\login.py', 'DATA'),
  ('ui.py', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\ui.py', 'DATA')],
 '3.12.9 | packaged by Anaconda, Inc. | (main, Feb  6 2025, 18:49:16) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\main.py', 'PYSOURCE')],
 [('pkg_resources',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\signal.py',
   'PYMODULE'),
  ('selectors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\struct.py',
   'PYMODULE'),
  ('logging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('copy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\copy.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\string.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\gettext.py',
   'PYMODULE'),
  ('urllib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\socket.py',
   'PYMODULE'),
  ('random',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\hashlib.py',
   'PYMODULE'),
  ('bisect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bisect.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\queue.py',
   'PYMODULE'),
  ('json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\runpy.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\netrc.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\py_compile.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('shlex',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\shlex.py',
   'PYMODULE'),
  ('http.server',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tty.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\glob.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('dis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\opcode.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\typing.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tempfile.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('inspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\inspect.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('__future__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\__future__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pathlib.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('database',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\database.py',
   'PYMODULE'),
  ('config',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\config.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('ui', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\ui.py', 'PYMODULE'),
  ('Repairing',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\Repairing.py',
   'PYMODULE'),
  ('pandas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pickletools.py',
   'PYMODULE'),
  ('doctest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pdb.py',
   'PYMODULE'),
  ('code',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\cmd.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('uuid',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\uuid.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pytz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('login', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\login.py', 'PYMODULE'),
  ('excel_importer',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\excel_importer.py',
   'PYMODULE')],
 [('python312.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\python312.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('ffi.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libexpat.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('Repairing.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\Repairing.py',
   'DATA'),
  ('config.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\config.py',
   'DATA'),
  ('database.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\database.py',
   'DATA'),
  ('excel_importer.py',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\excel_importer.py',
   'DATA'),
  ('login.py', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\login.py', 'DATA'),
  ('ui.py', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\ui.py', 'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\build\\main\\base_library.zip',
   'DATA')])
