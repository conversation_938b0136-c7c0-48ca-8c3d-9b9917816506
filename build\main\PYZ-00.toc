('D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\build\\main\\PYZ-00.pyz',
 [('PyQt5',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('Repairing',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\Repairing.py',
   'PYMODULE'),
  ('__future__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\codeop.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('database',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\database.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('excel_importer',
   'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\excel_importer.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('login', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\login.py', 'PYMODULE'),
  ('lzma',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pytz',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\random.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\typing.py',
   'PYMODULE'),
  ('ui', 'D:\\桌面\\develop\\机加件订单管理系统\\maching manage\\ui.py', 'PYMODULE'),
  ('unittest',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('xml',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\ProgramData\\anaconda3\\envs\\Jijia_Order\\Lib\\zipimport.py',
   'PYMODULE')])
