# 分析Excel文件内容

import pandas as pd
import os
from openpyxl import load_workbook

def analyze_excel(file_path):
    print(f"分析Excel文件: {file_path}")
    print(f"文件是否存在: {os.path.exists(file_path)}")
    
    # 使用openpyxl检查表头
    try:
        wb = load_workbook(file_path, data_only=True)
        sheet = wb.active
        print("\n检查关键单元格内容:")
        required_cells = {
            'B13': '序号',
            'C13': '生产订单编号',
            'D13': '产品编码',
            'F13': '产品名称',
            'G13': '项目号',
            'H13': '图号/型号',
            'I13': '材料',
            'J13': '生产数量',
            'K13': 'MRP数量',
            'N13': '生产单位',
            'R13': '存储地点',
            'S13': '计划开工日',
            'U13': '计划完工日'
        }
        
        for cell, expected_value in required_cells.items():
            actual_value = sheet[cell].value
            match = actual_value == expected_value
            print(f"单元格 {cell}: 期望值='{expected_value}', 实际值='{actual_value}', 匹配={match}")
    except Exception as e:
        print(f"检查表头出错: {e}")
    
    # 使用pandas读取数据
    try:
        print("\n使用pandas读取数据:")
        try:
            df = pd.read_excel(file_path, header=12)  # Excel第13行对应header=12
            print(f"成功读取{len(df)}行数据")
        except Exception as e:
            print(f"读取失败，尝试调整header位置。错误信息: {e}")
            df = pd.read_excel(file_path, header=None)
            print("原始数据预览:\n", df.head())
        print(f"表格列名: {list(df.columns)}")
        print(f"表格行数: {len(df)}")
        print(f"生产订单编号列的非空值数量: {df['生产订单编号'].notna().sum()}")
        
        # 打印前几行数据
        print("\n表格前5行数据:")
        if len(df) > 0:
            print(df.head(5).to_string())
        else:
            print("表格没有数据行")
            
        # 检查每列的非空值数量
        print("\n各列非空值数量:")
        for col in df.columns:
            non_null_count = df[col].notna().sum()
            print(f"列 '{col}': {non_null_count}个非空值")
    except Exception as e:
        print(f"读取Excel数据出错: {e}")

if __name__ == "__main__":
    analyze_excel("表2.xlsx")