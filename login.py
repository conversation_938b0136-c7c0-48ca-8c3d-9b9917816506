# 登录界面模块

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QMessageBox, QTabWidget, QWidget, QFormLayout,
                             QDialogButtonBox, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QFont
from database import Database

class LoginDialog(QDialog):
    """登录对话框"""
    # 自定义信号，登录成功时发射，传递用户信息
    login_success = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = Database()
        self.current_user = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("UPTEC机加件订单管理系统 - 登录")
        self.setFixedSize(500, 800)  # 增加窗口尺寸
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        
        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)  # 增加外边距
        main_layout.setSpacing(20)  # 增加组件间距
        
        # 添加标题
        title_label = QLabel("机加件订单管理系统")
        title_label.setFont(QFont("Arial", 25, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMargin(10)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget(self)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #C0C0C0;
                border-radius: 4px;
                padding: 0px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #E0E0E0;
                border: 1px solid #C0C0C0;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 10px 18px;
                margin-right: 2px;
                font-size: 13px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: none;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background-color: #F0F0F0;
            }
        """)
        
        # 创建登录选项卡
        self.login_tab = QWidget()
        self.tab_widget.addTab(self.login_tab, "登录")
        
        # 创建修改密码选项卡
        self.change_pwd_tab = QWidget()
        self.tab_widget.addTab(self.change_pwd_tab, "修改密码")
        
        # 创建添加用户选项卡
        self.add_user_tab = QWidget()
        self.tab_widget.addTab(self.add_user_tab, "创建账户")
        
        # 设置登录选项卡的布局
        self.setup_login_tab()
        
        # 设置修改密码选项卡的布局
        self.setup_change_pwd_tab()
        
        # 设置添加用户选项卡的布局
        self.setup_add_user_tab()
        
        # 添加选项卡
        main_layout.addWidget(self.tab_widget)
        
        # 设置窗口样式
        self.setStyleSheet(self.styleSheet() + """
            QDialog {
                background-color: #F5F5F5;
            }
            QLineEdit {
                border: 1px solid #C0C0C0;
                border-radius: 4px;
                padding: 8px;
                min-height: 24px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
            }
            QLabel {
                color: #333333;
                font-size: 13px;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 1px solid #CCCCCC;
                border-radius: 6px;
                margin-top: 14px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                color: #444444;
            }
            QFormLayout {
                spacing: 15px;
            }
            QPushButton {
                padding: 8px 15px;
                font-size: 13px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
    
    def setup_login_tab(self):
        """设置登录选项卡的布局"""
        main_layout = QVBoxLayout(self.login_tab)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 20, 30, 20)
        
        # 创建登录表单区域
        login_group = QGroupBox("用户登录")
        login_form = QFormLayout(login_group)
        login_form.setContentsMargins(15, 25, 15, 15)
        login_form.setSpacing(18)
        login_form.setLabelAlignment(Qt.AlignLeft)  # 标签靠左对齐
        login_form.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        login_form.setHorizontalSpacing(15)
        
        # 用户名输入框
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        self.username_edit.setMinimumHeight(30)
        username_label = QLabel("用户名:")
        login_form.addRow(username_label, self.username_edit)
        
        # 密码输入框
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(30)
        password_label = QLabel("密码:")
        login_form.addRow(password_label, self.password_edit)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)
        
        self.exit_button = QPushButton("退出")
        self.exit_button.setMinimumWidth(90)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a33025;
            }
        """)
        self.exit_button.clicked.connect(self.reject)
        
        self.login_button = QPushButton("登录")
        self.login_button.setMinimumWidth(120)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.login_button.clicked.connect(self.handle_login)
        
        button_layout.addWidget(self.exit_button)
        button_layout.addSpacing(15)
        button_layout.addWidget(self.login_button)
        
        # 添加到主布局
        main_layout.addWidget(login_group)
        main_layout.addLayout(button_layout)
        main_layout.addStretch(1)
        
        # 添加提示信息
        #note_label = QLabel("默认管理员账户: admin / admin123")
        #note_label.setAlignment(Qt.AlignCenter)
        #note_label.setStyleSheet("color: #999999; font-size: 11px;")
        #main_layout.addWidget(note_label)
    
    def setup_change_pwd_tab(self):
        """设置修改密码选项卡的布局"""
        main_layout = QVBoxLayout(self.change_pwd_tab)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 20, 30, 20)
        
        # 创建修改密码表单区域
        pwd_group = QGroupBox("修改密码")
        pwd_form = QFormLayout(pwd_group)
        pwd_form.setContentsMargins(15, 25, 15, 15)
        pwd_form.setSpacing(18)
        pwd_form.setLabelAlignment(Qt.AlignLeft)  # 标签靠左对齐
        pwd_form.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        pwd_form.setHorizontalSpacing(15)
        
        # 创建标签和输入框
        username_label = QLabel("用户名:")
        old_pwd_label = QLabel("旧密码:")
        new_pwd_label = QLabel("新密码:")
        confirm_pwd_label = QLabel("确认新密码:")
        
        # 用户名输入框
        self.change_username_edit = QLineEdit()
        self.change_username_edit.setPlaceholderText("请输入用户名")
        self.change_username_edit.setMinimumHeight(30)
        pwd_form.addRow(username_label, self.change_username_edit)
        
        # 旧密码输入框
        self.old_password_edit = QLineEdit()
        self.old_password_edit.setPlaceholderText("请输入旧密码")
        self.old_password_edit.setEchoMode(QLineEdit.Password)
        self.old_password_edit.setMinimumHeight(30)
        pwd_form.addRow(old_pwd_label, self.old_password_edit)
        
        # 新密码输入框
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setPlaceholderText("请输入新密码")
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setMinimumHeight(30)
        pwd_form.addRow(new_pwd_label, self.new_password_edit)
        
        # 确认新密码输入框
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("请再次输入新密码")
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setMinimumHeight(30)
        pwd_form.addRow(confirm_pwd_label, self.confirm_password_edit)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)
        
        self.reset_pwd_button = QPushButton("重置")
        self.reset_pwd_button.setMinimumWidth(90)
        self.reset_pwd_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a33025;
            }
        """)
        self.reset_pwd_button.clicked.connect(self.reset_change_pwd_form)
        
        self.change_button = QPushButton("修改密码")
        self.change_button.setMinimumWidth(120)
        self.change_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.change_button.clicked.connect(self.handle_change_password)
        
        button_layout.addWidget(self.reset_pwd_button)
        button_layout.addSpacing(15)
        button_layout.addWidget(self.change_button)
        
        # 添加到主布局
        main_layout.addWidget(pwd_group)
        main_layout.addLayout(button_layout)
        main_layout.addStretch(1)
    
    def reset_change_pwd_form(self):
        """重置修改密码表单"""
        self.change_username_edit.clear()
        self.old_password_edit.clear()
        self.new_password_edit.clear()
        self.confirm_password_edit.clear()
        self.change_username_edit.setFocus()
    
    def handle_change_password(self):
        """处理修改密码请求"""
        username = self.change_username_edit.text().strip()
        old_password = self.old_password_edit.text()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        # 验证输入
        if not username or not old_password or not new_password or not confirm_password:
            QMessageBox.warning(self, "警告", "所有字段都不能为空！")
            return
        
        # 验证新密码一致性
        if new_password != confirm_password:
            QMessageBox.warning(self, "警告", "两次输入的新密码不一致！")
            self.new_password_edit.clear()
            self.confirm_password_edit.clear()
            self.new_password_edit.setFocus()
            return
        
        # 验证旧密码
        user = self.db.verify_user(username, old_password)
        if not user:
            QMessageBox.critical(self, "错误", "用户名或旧密码错误！")
            self.old_password_edit.clear()
            self.old_password_edit.setFocus()
            return
        
        # 更新密码
        if self.db.update_user_password(username, new_password):
            QMessageBox.information(self, "成功", "密码修改成功！")
            self.reset_change_pwd_form()
        else:
            QMessageBox.critical(self, "错误", "密码修改失败，请稍后重试！")
    
    def handle_login(self):
        """处理登录请求"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "用户名和密码不能为空！")
            return
        
        # 验证用户
        user = self.db.verify_user(username, password)
        
        if user:
            self.current_user = user
            # 发射登录成功信号
            self.login_success.emit(user)
            self.accept()
        else:
            QMessageBox.critical(self, "错误", "用户名或密码错误！")
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    def setup_add_user_tab(self):
        """设置添加用户选项卡的布局"""
        # 初始化输入框组件
        self.new_username_input = QLineEdit()
        self.new_password_input = QLineEdit()
        self.confirm_password_input = QLineEdit()
        self.new_realname_input = QLineEdit()
        self.new_department_input = QLineEdit()
    
        # 创建主布局
        main_layout = QVBoxLayout(self.add_user_tab)
        main_layout.setSpacing(25)  # 减少垂直间距
        main_layout.setContentsMargins(25, 25, 25, 25)  # 减少边距
        
        # 创建管理员验证区域
        admin_group = QGroupBox("管理员验证")
        admin_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 1px solid #CCCCCC;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                color: #444444;
            }
        """)
        admin_layout = QVBoxLayout(admin_group)
        admin_layout.setContentsMargins(20, 25, 20, 20)  # 减少内边距
        admin_layout.setSpacing(20)  # 减少垂直间距
        
        # 管理员用户名行
        admin_username_layout = QHBoxLayout()
        admin_username_layout.setSpacing(15)  # 减少水平间距
        admin_username_label = QLabel("管理员用户名:")
        admin_username_label.setMinimumWidth(120)  # 适当标签宽度
        admin_username_label.setStyleSheet("font-size: 13px; font-weight: bold;")
        self.admin_username_input = QLineEdit()
        self.admin_username_input.setPlaceholderText("请输入管理员用户名")
        self.admin_username_input.setMinimumHeight(30)  # 标准输入框高度
        self.admin_username_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #C0C0C0;
                border-radius: 4px;
                padding: 8px;
                font-size: 13px;
            }
        """)
        admin_username_layout.addWidget(admin_username_label)
        admin_username_layout.addWidget(self.admin_username_input)
        
        # 管理员密码行
        admin_password_layout = QHBoxLayout()
        admin_password_layout.setSpacing(15)  # 减少水平间距
        admin_password_label = QLabel("管理员密码:")
        admin_password_label.setMinimumWidth(120)
        admin_password_label.setStyleSheet("font-size: 13px; font-weight: bold;")
        self.admin_password_input = QLineEdit()
        self.admin_password_input.setPlaceholderText("请输入管理员密码")
        self.admin_password_input.setEchoMode(QLineEdit.Password)
        self.admin_password_input.setMinimumHeight(30)
        self.admin_password_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #C0C0C0;
                border-radius: 4px;
                padding: 8px;
                font-size: 13px;
            }
        """)
        admin_password_layout.addWidget(admin_password_label)
        admin_password_layout.addWidget(self.admin_password_input)
        
        # 添加到管理员验证布局
        admin_layout.addLayout(admin_username_layout)
        admin_layout.addLayout(admin_password_layout)
        
        # 创建新用户信息区域
        user_group = QGroupBox("新用户信息")
        user_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 1px solid #CCCCCC;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                color: #444444;
            }
        """)
        user_layout = QVBoxLayout(user_group)
        user_layout.setContentsMargins(20, 25, 20, 20)  # 减少内边距
        user_layout.setSpacing(20)  # 减少垂直间距
        
        # 创建布局变量
        username_layout = QHBoxLayout()
        password_layout = QHBoxLayout()
        confirm_layout = QHBoxLayout()
        realname_layout = QHBoxLayout()
        department_layout = QHBoxLayout()
        
        # 创建各行布局并设置间距
        for layout in [username_layout, password_layout, confirm_layout, 
                      realname_layout, department_layout]:
            layout.setSpacing(15)  # 减少水平间距
        
        # 设置标签和输入框
        labels_inputs = [
            ("用户名:", self.new_username_input, "请输入新用户名"),
            ("密码:", self.new_password_input, "请输入新密码"),
            ("确认密码:", self.confirm_password_input, "请再次输入密码"),
            ("真实姓名:", self.new_realname_input, "请输入真实姓名"),
            ("部门:", self.new_department_input, "请输入部门")
        ]
        
        layouts = [username_layout, password_layout, confirm_layout, 
                  realname_layout, department_layout]
        
        # 统一输入框样式
        input_style = """
            QLineEdit {
                border: 1px solid #C0C0C0;
                border-radius: 4px;
                padding: 8px;
                font-size: 13px;
            }
        """
        
        for layout, (label_text, input_widget, placeholder) in zip(layouts, labels_inputs):
            label = QLabel(label_text)
            label.setMinimumWidth(120)
            label.setStyleSheet("font-size: 13px; font-weight: bold;")
            
            input_widget.setPlaceholderText(placeholder)
            input_widget.setMinimumHeight(30)
            input_widget.setStyleSheet(input_style)
            
            if "密码" in label_text:
                input_widget.setEchoMode(QLineEdit.Password)
                
            layout.addWidget(label)
            layout.addWidget(input_widget)
            user_layout.addLayout(layout)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)  # 减少按钮间距
        button_layout.addStretch(1)
        
        # 创建重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.setMinimumWidth(100)
        self.reset_button.setMinimumHeight(35)  # 标准按钮高度
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        self.reset_button.clicked.connect(self.reset_add_user_form)
        
        # 创建创建账户按钮
        self.create_account_button = QPushButton("创建账户")
        self.create_account_button.setMinimumWidth(120)
        self.create_account_button.setMinimumHeight(35)
        self.create_account_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        self.create_account_button.clicked.connect(self.create_account)
        
        button_layout.addWidget(self.reset_button)
        button_layout.addSpacing(10)  # 减少按钮之间的间距
        button_layout.addWidget(self.create_account_button)
        
        # 添加所有组件到主布局
        main_layout.addWidget(admin_group)
        main_layout.addSpacing(25)  # 适当组间距
        main_layout.addWidget(user_group)
        main_layout.addSpacing(20)  # 适当按钮区域间距
        main_layout.addLayout(button_layout)
        # main_layout.addStretch(1)  # 移除底部弹性空间
    
    def create_account(self):
        """处理创建用户请求"""
        # 验证管理员身份
        admin_username = self.admin_username_input.text().strip()
        admin_password = self.admin_password_input.text()
        if not admin_username or not admin_password:
            QMessageBox.warning(self, "警告", "请输入管理员用户名和密码！")
            return
        
        admin_user = self.db.verify_user(admin_username, admin_password)
        if not admin_user or admin_user['role'] != 'admin':
            QMessageBox.critical(self, "错误", "管理员密码错误或权限不足！")
            self.admin_username_input.clear()
            self.admin_password_input.clear()
            self.admin_username_input.setFocus()
            return
        
        # 验证新用户信息
        username = self.new_username_input.text().strip()
        password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()
        real_name = self.new_realname_input.text().strip()
        department = self.new_department_input.text().strip()
        
        # 验证必填字段
        if not username or not password or not confirm_password or not real_name or not department:
            QMessageBox.warning(self, "警告", "所有字段均为必填项！")
            return
        
        # 验证密码匹配
        if password != confirm_password:
            QMessageBox.warning(self, "警告", "两次输入的密码不匹配！")
            self.new_password_input.clear()
            self.confirm_password_input.clear()
            self.new_password_input.setFocus()
            return
        
        # 验证密码长度
        if len(password) < 6:
            QMessageBox.warning(self, "警告", "密码长度至少为6位！")
            self.new_password_input.clear()
            self.confirm_password_input.clear()
            self.new_password_input.setFocus()
            return
        
        # 创建新用户
        user_id = self.db.add_user(username, password, "user", real_name, department)
        
        if user_id:
            QMessageBox.information(self, "成功", f"用户 {username} 创建成功！")
            
            # 清空所有字段
            self.admin_username_input.clear()
            self.admin_password_input.clear()
            self.new_username_input.clear()
            self.new_password_input.clear()
            self.confirm_password_input.clear()
            self.new_realname_input.clear()
            self.new_department_input.clear()
            
            # 切换到登录选项卡
            self.tab_widget.setCurrentIndex(0)
        else:
            QMessageBox.critical(self, "错误", f"创建用户失败！用户名 {username} 可能已存在。")
            self.new_username_input.setFocus()
    
    def reset_add_user_form(self):
        """重置创建账户表单"""
        self.admin_username_input.clear()
        self.admin_password_input.clear()
        self.new_username_input.clear()
        self.new_password_input.clear()
        self.confirm_password_input.clear()
        self.new_realname_input.clear()
        self.new_department_input.clear()
        
        # 将焦点设置到管理员用户名输入框
        self.admin_username_input.setFocus()