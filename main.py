#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 主程序入口

import sys
from PyQt5.QtWidgets import QApplication
from ui import MainWindow
from database import Database

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 设置应用风格为Fusion，提供一致的跨平台外观
    
    # 启动时只做一次表结构创建 - 已移除
    db = Database() # 实例化数据库连接
    # db.create_tables() # 移除此行
    
    # 创建主窗口
    window = MainWindow()
    
    # 执行应用
    sys.exit(app.exec_())
    
def on_add_order_finished(self, record):
    # 添加订单后直接全量刷新表格，避免单行插入卡顿
    self.load_data()
    