# 数据库操作模块

import pymysql
import datetime
from config import DB_CONFIG
import threading

class Database:
    def __init__(self):
        self.conn = None
        self.cursor = None
        self.connect()

    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.conn = pymysql.connect(
                host=DB_CONFIG['host'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                charset=DB_CONFIG['charset']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            # 尝试选择数据库，如果不存在则报错
            try:
                self.conn.select_db(DB_CONFIG['database'])
                print(f"成功连接到数据库 {DB_CONFIG['database']}")
            except pymysql.err.OperationalError as db_error:
                if db_error.args[0] == 1049: # Unknown database
                    print(f"错误: 数据库 '{DB_CONFIG['database']}' 不存在。请先手动创建数据库。")
                    # 可以选择退出程序或抛出异常
                    raise ConnectionError(f"数据库 '{DB_CONFIG['database']}' 不存在") from db_error
                else:
                    raise # 重新抛出其他数据库错误
            return True
        except Exception as e:
            print(f"数据库连接错误: {e}")
            return False
    
    def insert_order(self, data):
        """插入新订单数据"""
        try:
            # 过滤掉None值，将其转换为SQL的NULL
            filtered_data = {}
            for key, value in data.items():
                if value is not None:
                    filtered_data[key] = value
                else:
                    filtered_data[key] = None
            
            columns = ", ".join(filtered_data.keys())
            placeholders = ", ".join(["%s"] * len(filtered_data))
            values = tuple(filtered_data.values())
            
            query = f"INSERT INTO machine_orders ({columns}) VALUES ({placeholders})"
            self.cursor.execute(query, values)
            self.conn.commit()
            last_id = self.cursor.lastrowid
            print(f"成功插入数据，ID: {last_id}")
            return last_id
        except Exception as e:
            print(f"插入数据错误: {e}")
            self.conn.rollback()
            return False
    
    def get_all_orders(self):
        """获取所有订单数据（每次新建连接，确保拿到最新数据）"""
        import pymysql
        try:
            conn = pymysql.connect(
                host=DB_CONFIG['host'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                database=DB_CONFIG['database'],
                charset=DB_CONFIG['charset']
            )
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT * FROM machine_orders")
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            print(f"查询数据错误: {e}")
            return []
    
    def update_order(self, order_id, field, value):
        """更新订单特定字段"""
        try:
            # 总是尝试更新 update_time 列，如果不存在则数据库会报错，
            # 这符合不再检查列存在性的要求
            query = f"UPDATE machine_orders SET {field} = %s, update_time = NOW() WHERE id = %s"
            self.cursor.execute(query, (value, order_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"更新数据错误: {e}")
            self.conn.rollback()
            return False
    
    def check_column_exists(self, table, column):
        """检查表中是否存在指定列 (保留此方法以备内部使用，但不用于修复)
        
        Args:
            table: 表名
            column: 列名
            
        Returns:
            布尔值，表示列是否存在
        """
        try:
            self.cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = %s AND column_name = %s
            """, (DB_CONFIG['database'], table, column))
            result = self.cursor.fetchone()
            return result['COUNT(*)'] > 0
        except Exception as e:
            print(f"检查列错误: {e}")
            return False
    
    def update_processing_completion_time(self, order_ids):
        """更新加工完成时间"""
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ids_str = ", ".join([str(id) for id in order_ids])
            query = f"UPDATE machine_orders SET processing_completion_time = %s, update_time = NOW() WHERE id IN ({ids_str})"
            self.cursor.execute(query, (current_time,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"更新加工完成时间错误: {e}")
            self.conn.rollback()
            return False
    
    def update_storage_date(self, order_ids):
        """更新入库日期"""
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ids_str = ", ".join([str(id) for id in order_ids])
            query = f"UPDATE machine_orders SET storage_date = %s, update_time = NOW() WHERE id IN ({ids_str})"
            self.cursor.execute(query, (current_time,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"更新入库日期错误: {e}")
            self.conn.rollback()
            return False
    
    def delete_orders(self, order_ids, username="system"):
        """删除订单并记录到日志表
        
        Args:
            order_ids: 订单ID或订单ID列表
            username: 执行删除操作的用户名
        """
        try:
            # 验证输入参数
            if not order_ids:
                print("警告: 尝试删除空ID列表")
                return False
            
            # 确保order_ids是一个列表
            if not isinstance(order_ids, list):
                print(f"将单个ID {order_ids} 转换为列表")
                order_ids = [order_ids]
                
            # 过滤掉None值和非数字ID
            valid_ids = [str(int(id)) for id in order_ids if id is not None and str(id).isdigit()]
            if not valid_ids:
                print("错误: 没有有效的ID可以删除")
                return False
                
            print(f"准备删除记录，有效ID: {valid_ids}")
                
            # 先获取要删除的记录
            ids_str = ", ".join(valid_ids)
            self.cursor.execute(f"SELECT * FROM machine_orders WHERE id IN ({ids_str})")
            records = self.cursor.fetchall()
            
            if not records:
                print(f"警告: 未找到ID为 {ids_str} 的记录")
                return False
                
            print(f"找到 {len(records)} 条记录将被删除")
            
            # 不再检查或创建删除日志表
            # self.create_delete_logs_table()
            
            # 所有记录使用同一个删除时间，确保批量删除的记录有一致的时间戳
            delete_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 记录到日志表
            for record in records:
                try:
                    record_id = record['id']
                    print(f"处理记录ID: {record_id}, 字段: {list(record.keys())}")
                    
                    # 创建一个字典来存储日志记录字段
                    log_record = {
                        'record_id': record_id,
                        'delete_time': delete_time,  # 使用统一的删除时间
                        'deleted_by': username
                    }
                    
                    # 将记录中的字段复制到日志记录中，排除不需要的字段
                    excluded_fields = ['id', 'checkbox', 'update_time', 'est_cnc_hours', 'est_lathe_hours', 'est_wire_hours', 'status']
                    for key, value in record.items():
                        if key not in excluded_fields:
                            log_record[key] = value
                    
                    print(f"日志记录包含字段: {list(log_record.keys())}")
                    
                    # 插入日志
                    columns = ", ".join(log_record.keys())
                    placeholders = ", ".join(["%s"] * len(log_record))
                    values = tuple(log_record.values())
                    
                    query = f"INSERT INTO delete_logs ({columns}) VALUES ({placeholders})"
                    print(f"执行SQL: {query}")
                    self.cursor.execute(query, values)
                    print(f"记录ID {record_id} 已写入日志表")
                except Exception as inner_e:
                    print(f"记录日志错误: {inner_e}")
                    # 继续处理其他记录
            
            # 删除记录
            delete_query = f"DELETE FROM machine_orders WHERE id IN ({ids_str})"
            print(f"执行删除SQL: {delete_query}")
            self.cursor.execute(delete_query)
            self.conn.commit()
            print(f"成功删除记录并记录到日志，ID: {ids_str}")
            return True
        except Exception as e:
            print(f"删除数据错误: {e}")
            self.conn.rollback()
            return False
    
    def get_delete_logs(self):
        """获取删除日志"""
        try:
            # 不再检查或创建删除日志表
            # self.create_delete_logs_table()
            
            print("查询删除日志表中的记录")
            
            # 查询数据
            self.cursor.execute("SELECT COUNT(*) FROM delete_logs")
            count_result = self.cursor.fetchone()
            records_count = count_result['COUNT(*)']
            print(f"删除日志表中有 {records_count} 条记录")
            
            if records_count == 0:
                return []
                
            # 查询所有记录
            self.cursor.execute("SELECT * FROM delete_logs ORDER BY delete_time DESC")
            logs = self.cursor.fetchall()
            print(f"成功获取 {len(logs)} 条删除日志记录")
            
            # 打印第一条记录的字段（如果存在）
            if logs and len(logs) > 0:
                print(f"第一条记录字段: {list(logs[0].keys())}")
            
            return logs
        except Exception as e:
            print(f"查询删除日志错误: {e}")
            # 如果查询失败，不再尝试重建表
            # try:
            #     print("尝试重建删除日志表")
            #     self.cursor.execute("DROP TABLE IF EXISTS delete_logs")
            #     self.conn.commit()
            #     self.create_delete_logs_table()
            # except Exception as rebuild_error:
            #     print(f"重建删除日志表失败: {rebuild_error}")
            return []
    
    def add_order(self, data):
        """添加新订单(兼容旧接口)"""
        return self.insert_order(data)
        
    def create_empty_order(self):
        """创建空订单记录
        
        Returns:
            新创建的订单ID
        """
        try:
            # 生成临时MO编号
            temp_mo = f"TEMP-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # 直接插入，包含 update_time
            query = """
            INSERT INTO machine_orders 
            (mo_number, update_time) 
            VALUES (%s, NOW())
            """
            self.cursor.execute(query, (temp_mo,))
                
            self.conn.commit()
            
            # 返回新创建的ID
            return self.cursor.lastrowid
        except Exception as e:
            print(f"创建空订单错误: {e}")
            self.conn.rollback()
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.cursor.close()
            self.conn.close()
    
    def get_records_modified_since(self, timestamp):
        """获取自指定时间以来修改过的记录
        
        Args:
            timestamp: QDateTime对象，指定时间点
            
        Returns:
            记录列表
        """
        try:
            # 将QDateTime转换为MySQL日期时间格式
            time_str = timestamp.toString('yyyy-MM-dd HH:mm:ss')
            
            # 不再检查update_time列是否存在
            # if not self.check_column_exists('machine_orders', 'update_time'):
            #     print("警告: update_time列不存在，无法获取修改记录")
            #     return []
            
            # 查询自指定时间以来修改过的记录
            query = """
            SELECT * FROM machine_orders 
            WHERE update_time > %s
            """
            self.cursor.execute(query, (time_str,))
            return self.cursor.fetchall()
        except Exception as e:
            print(f"获取修改记录错误: {e}")
            return []
    
    def get_order_by_id(self, order_id):
        """根据ID获取订单记录
        
        Args:
            order_id: 订单ID
            
        Returns:
            订单记录，未找到则返回None
        """
        try:
            query = "SELECT * FROM machine_orders WHERE id = %s"
            self.cursor.execute(query, (order_id,))
            result = self.cursor.fetchone()
            return result
        except Exception as e:
            print(f"获取订单记录错误: {e}")
            return None
    
    # --- 用户验证相关方法 --- 
    def verify_user(self, username, password):
        """验证用户名和密码
        
        Args:
            username: 用户名
            password: 密码(明文)
            
        Returns:
            用户信息字典，如果验证失败则返回None
        """
        try:
            # 使用SHA-256哈希密码进行比较
            import hashlib
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # 查询用户
            self.cursor.execute("""
            SELECT id, username, role, real_name, department, status
            FROM users
            WHERE username = %s AND password = %s
            """, (username, hashed_password))
            
            user = self.cursor.fetchone()
            
            if user:
                # 如果账户被锁定或禁用
                if user['status'] != 'active':
                    print(f"用户 {username} 账户状态: {user['status']}")
                    return None
                
                # 更新最后登录时间
                self.cursor.execute("""
                UPDATE users
                SET last_login = NOW()
                WHERE id = %s
                """, (user['id'],))
                self.conn.commit()
                
                print(f"用户 {username} 验证成功")
                return user
            else:
                print(f"用户 {username} 验证失败")
                return None
        except Exception as e:
            print(f"验证用户错误: {e}")
            return None
            
    def change_password(self, username, old_password, new_password):
        """修改用户密码
        
        Args:
            username: 用户名
            old_password: 旧密码(明文)
            new_password: 新密码(明文)
            
        Returns:
            布尔值，表示密码修改是否成功
        """
        try:
            # 先验证旧密码
            # 验证用户 - 直接调用自身验证逻辑
            import hashlib
            hashed_old_password = hashlib.sha256(old_password.encode()).hexdigest()
            self.cursor.execute("SELECT id FROM users WHERE username = %s AND password = %s", (username, hashed_old_password))
            user_check = self.cursor.fetchone()
            
            if not user_check:
                print(f"用户 {username} 修改密码失败：旧密码验证失败")
                return False
            
            # 哈希新密码
            hashed_password = hashlib.sha256(new_password.encode()).hexdigest()
            
            # 更新密码
            self.cursor.execute("""
            UPDATE users
            SET password = %s, updated_at = NOW()
            WHERE username = %s
            """, (hashed_password, username))
            self.conn.commit()
            
            print(f"用户 {username} 密码修改成功")
            return True
        except Exception as e:
            print(f"修改密码错误: {e}")
            self.conn.rollback()
            return False
            
    def add_user(self, username, password, role="user", real_name="", department=""):
        """添加新用户
        
        Args:
            username: 用户名
            password: 密码(明文)
            role: 角色权限 ('admin' 或 'user')
            real_name: 真实姓名
            department: 部门
            
        Returns:
            新用户ID，失败则返回None
        """
        try:
            # 检查用户名是否已存在
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = %s", (username,))
            if self.cursor.fetchone()['COUNT(*)'] > 0:
                print(f"用户 {username} 已存在，无法添加")
                return None
            
            # 哈希密码
            import hashlib
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # 插入新用户
            self.cursor.execute("""
            INSERT INTO users (username, password, role, real_name, department, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
            """, (username, hashed_password, role, real_name, department))
            self.conn.commit()
            
            user_id = self.cursor.lastrowid
            print(f"成功添加用户 {username}，ID: {user_id}")
            return user_id
        except Exception as e:
            print(f"添加用户错误: {e}")
            self.conn.rollback()
            return None

    def update_record(self, record_id, fields_data):
        """更新记录的多个字段
        
        Args:
            record_id: 记录ID
            fields_data: 字典，键为字段名，值为字段值
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            if not fields_data:
                return False
            
            # 构建更新字段列表
            update_parts = []
            values = []
            
            for field, value in fields_data.items():
                update_parts.append(f"{field} = %s")
                values.append(value)
            
            # 总是尝试更新 update_time 列
            update_parts.append("update_time = NOW()")
            
            # 构建SQL语句
            query = f"UPDATE machine_orders SET {', '.join(update_parts)} WHERE id = %s"
            values.append(record_id)
            
            # 执行更新
            self.cursor.execute(query, tuple(values))
            self.conn.commit()
            
            return True
        except Exception as e:
            print(f"更新记录错误: {e}")
            self.conn.rollback()
            return False

    def check_and_repair_database_once(self):
        # 此方法内容已移除
        print("数据库创建和修复功能已移除。请确保数据库已手动创建。")
        pass

    def update_user_password(self, username, new_password):
        """直接更新用户密码（用于修改密码功能）
        
        Args:
            username: 用户名
            new_password: 新密码(明文)
            
        Returns:
            布尔值，表示密码修改是否成功
        """
        try:
            # 哈希新密码
            import hashlib
            hashed_password = hashlib.sha256(new_password.encode()).hexdigest()
            
            # 更新密码
            self.cursor.execute("""
            UPDATE users
            SET password = %s, updated_at = NOW()
            WHERE username = %s
            """, (hashed_password, username))
            self.conn.commit()
            
            print(f"用户 {username} 密码修改成功")
            return True
        except Exception as e:
            print(f"更新密码错误: {e}")
            self.conn.rollback()
            return False

    def get_all_repairing_records(self):
        """获取所有返修记录"""
        import pymysql
        try:
            # 每次查询都建立新连接，确保获取最新数据
            conn = pymysql.connect(
                host=DB_CONFIG['host'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                database=DB_CONFIG['database'],
                charset=DB_CONFIG['charset']
            )
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            # 修改排序方式为 ASC (升序)，让新记录显示在底部
            cursor.execute("SELECT * FROM repairing ORDER BY id ASC")
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            print(f"成功获取 {len(result)} 条返修记录")
            return result
        except pymysql.err.ProgrammingError as pe:
            # 特别处理表不存在的错误
            if pe.args[0] == 1146: # Table doesn't exist
                print(f"错误: 返修表 'repairing' 不存在。")
                # 可以返回空列表或重新抛出特定异常
                return []
            else:
                print(f"查询返修数据SQL错误: {pe}")
                return []
        except Exception as e:
            print(f"查询返修数据错误: {e}")
            return []

    def add_empty_repairing_record(self):
        """在 repairing 表中创建一条空的返修记录

        Returns:
            新创建记录的字典，如果失败则返回 None
        """
        try:
            # 定义所有需要插入的字段及其默认值
            # 使用截图中的确切列名
            default_values = {
                'ProjectNumber': '未指定',       # Corrected column name
                'MaterialNumber': '未指定',      # Corrected column name
                'MaterialName': '新返修记录',   # Corrected column name
                'Qty': '0',                    # Assuming Qty is varchar based on screenshot, provide as string
                'RepairingContent': '',    # Corrected column name
                'ArrivingDate': datetime.date.today().strftime('%Y-%m-%d'), # Format date as string for varchar
                'FinishDate': '',             # Assuming empty string for varchar, or None if NULL allowed
                'RepairingPerson': '',     # Corrected column name
                'RepairingTime': '0.0',        # Assuming RepairingTime is varchar, provide as string
                'TotalTime': '0.0',          # Assuming TotalTime is varchar, provide as string
                'Classification': ''       # Corrected column name
                # Add other NOT NULL fields if necessary
            }

            # 构建 SQL 语句 (这部分通常不需要改)
            columns = ', '.join([f"`{col}`" for col in default_values.keys()]) # Add backticks for safety
            placeholders = ', '.join(['%s'] * len(default_values))
            values = tuple(default_values.values())

            query = f"""
            INSERT INTO repairing ({columns})
            VALUES ({placeholders})
            """

            print(f"Executing add empty record query: {query} with values: {values}") # Debug info
            self.cursor.execute(query, values)
            new_id = self.cursor.lastrowid
            self.conn.commit()

            if new_id:
                # 获取刚插入的完整记录
                self.cursor.execute("SELECT * FROM repairing WHERE id = %s", (new_id,))
                new_record = self.cursor.fetchone()
                print(f"成功添加空返修记录，ID: {new_id}")
                # No date conversion needed here as they are likely varchar
                return new_record
            else:
                print("添加空返修记录失败，未能获取新ID")
                self.conn.rollback() # Ensure rollback
                return None
        except Exception as e:
            print(f"添加空返修记录错误: {e}") # Print detailed error
            self.conn.rollback()
            return None

    def delete_repairing_records(self, record_ids):
        """从 repairing 表中删除指定的记录

        Args:
            record_ids: 要删除的记录 ID 列表

        Returns:
            bool: 删除是否成功
        """
        if not record_ids:
            print("警告: 尝试删除空的返修记录ID列表")
            return False

        # Ensure IDs are integers and create placeholders
        try:
            # 确保 ID 是整数
            valid_ids = [int(id) for id in record_ids if id is not None]
            if not valid_ids:
                 print("错误: 没有有效的返修记录ID可以删除")
                 return False
            placeholders = ', '.join(['%s'] * len(valid_ids))
            # 添加反引号以确保列名安全，即使 id 是标准名称
            query = f"DELETE FROM repairing WHERE `id` IN ({placeholders})"

            print(f"Executing delete query: {query} with IDs: {valid_ids}") # Debug info
            result = self.cursor.execute(query, tuple(valid_ids))
            self.conn.commit()
            print(f"成功删除了 {result} 条返修记录")
            return True
        except ValueError as ve:
             print(f"删除返修记录错误: ID列表包含无效值 - {ve}")
             return False
        except Exception as e:
            print(f"删除返修记录错误: {e}")
            self.conn.rollback()
            return False

    def update_repairing_record(self, record_id, field_name, new_value):
        """更新 repairing 表中的单个字段

        Args:
            record_id (int): 要更新的记录ID
            field_name (str): 要更新的数据库字段名
            new_value (any): 新的值

        Returns:
            bool: 更新是否成功
        """
        if field_name == 'id': # 不允许直接修改ID
             print("错误：不允许修改记录ID")
             return False
        try:
            # 使用反引号保护字段名
            query = f"UPDATE repairing SET `{field_name}` = %s WHERE id = %s"
            print(f"Executing update repairing: {query} with values: ({new_value}, {record_id})") # Debug
            result = self.cursor.execute(query, (new_value, record_id))
            self.conn.commit()
            print(f"更新返修记录 {record_id} 字段 '{field_name}' 结果: {result}")
            return True # 或者可以检查 result > 0
        except Exception as e:
            print(f"更新返修记录错误 (ID: {record_id}, 字段: {field_name}): {e}")
            self.conn.rollback()
            return False