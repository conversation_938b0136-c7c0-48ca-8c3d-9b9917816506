# Repairing.py - 返修统计页面

import sys
import os
import datetime # 添加 datetime 导入
from PyQt5.QtWidgets import (QDialog, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QFileDialog, QMessageBox, QCheckBox, QLabel, QLineEdit,
                             QAbstractItemView, QMenu, QAction, QScrollArea, QFrame, QToolButton,
                             QStyledItemDelegate, QStyle)
from PyQt5.QtCore import (Qt, QDateTime, QEvent, QSize, QPoint, QRect, QTimer,
                         QSettings, pyqtSignal)
from PyQt5.QtGui import (QIcon, QCursor, QFont, QColor, QPainter, QLinearGradient, QPen,
                        QBrush, QPalette, QFontMetrics, QPainterPath, QIntValidator)

from database import Database # 导入 Database 类

# --- 从 ui.py 复制的辅助类 ---

class FilterButton(QToolButton):
    """自定义3D效果筛选按钮"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(18, 18)
        self.setCursor(Qt.PointingHandCursor)
        self.is_active = False
        self.is_hovered = False

    def paintEvent(self, event):
        """自定义绘制"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        if self.is_active:
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0, QColor(160, 210, 250))
            gradient.setColorAt(1, QColor(120, 180, 240))
            painter.setBrush(QBrush(gradient))
        elif self.is_hovered:
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0, QColor(240, 240, 240))
            gradient.setColorAt(1, QColor(220, 220, 220))
            painter.setBrush(QBrush(gradient))
        else:
            painter.setBrush(Qt.transparent)

        if self.is_active or self.is_hovered:
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(0, 0, self.width(), self.height(), 3, 3)

        painter.setPen(QPen(QColor(80, 80, 80), 1.2))
        triangle_width = 8
        triangle_height = 5
        x_start = (self.width() - triangle_width) // 2
        y_mid = self.height() // 2 + 1

        shadow_color = QColor(60, 60, 60, 120)
        painter.setPen(QPen(shadow_color, 1.2))
        shadow_path = QPainterPath()
        shadow_path.moveTo(x_start + 1, y_mid - 2)
        shadow_path.lineTo(x_start + triangle_width + 1, y_mid - 2)
        shadow_path.lineTo(x_start + triangle_width/2 + 1, y_mid + triangle_height - 2)
        shadow_path.lineTo(x_start + 1, y_mid - 2)
        painter.drawPath(shadow_path)

        if self.is_active:
            triangle_color = QColor(255, 255, 255)
        else:
            triangle_color = QColor(80, 80, 80)

        painter.setPen(QPen(triangle_color, 1.2))
        path = QPainterPath()
        path.moveTo(x_start, y_mid - 3)
        path.lineTo(x_start + triangle_width, y_mid - 3)
        path.lineTo(x_start + triangle_width/2, y_mid + triangle_height - 3)
        path.lineTo(x_start, y_mid - 3)
        painter.drawPath(path)

    def set_active(self, active):
        self.is_active = active
        self.update()

    def enterEvent(self, event):
        self.is_hovered = True
        self.update()
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.is_hovered = False
        self.update()
        super().leaveEvent(event)

class HeaderItemDelegate(QStyledItemDelegate):
    """自定义表头代理，用于3D绘制"""
    def paint(self, painter, option, index):
        painter.save()
        header_gradient = QLinearGradient(0, 0, 0, option.rect.height())
        header_gradient.setColorAt(0, QColor(250, 250, 250))
        header_gradient.setColorAt(1, QColor(230, 230, 230))
        painter.fillRect(option.rect, header_gradient)
        painter.setPen(QPen(QColor(210, 210, 210), 1))
        painter.drawLine(option.rect.bottomLeft(), option.rect.bottomRight())
        painter.drawLine(option.rect.topRight(), option.rect.bottomRight())
        text = index.data(Qt.DisplayRole)
        if text:
            font = painter.font()
            font.setBold(True)
            painter.setFont(font)
            painter.setPen(QPen(QColor(150, 150, 150, 40)))
            text_rect = option.rect.adjusted(6, 1, -25, -1) # 为筛选按钮留空间
            painter.drawText(text_rect.adjusted(1, 1, 1, 1), Qt.AlignLeft | Qt.AlignVCenter, text)
            painter.setPen(QPen(QColor(70, 70, 70)))
            painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)
        painter.restore()

class FilterHeaderView(QHeaderView):
    """支持过滤功能的表格头"""
    filter_applied = pyqtSignal() # 定义信号

    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        self.filters = {}
        self.filter_buttons = {}
        self.all_column_values = {}
        self.setSectionsClickable(True)
        self.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.setStretchLastSection(True)
        self.setStyleSheet("""
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #FAFAFA, stop:1 #E6E6E6);
                border: 1px solid #D2D2D2;
                border-left: none;
                padding-left: 4px;
                padding-right: 20px; /* 为筛选按钮留空间 */
                font-weight: bold;
                color: #424242;
                min-height: 28px;
            }
            QHeaderView::section:first {
                border-left: 1px solid #D2D2D2;
            }
        """)
        self.setItemDelegate(HeaderItemDelegate())
        self.viewport().installEventFilter(self)

    def eventFilter(self, obj, event):
        if obj is self.viewport():
            if event.type() in [QEvent.Resize, QEvent.Move, QEvent.Paint, QEvent.Show]:
                QTimer.singleShot(0, self.update_filter_buttons)
        return super().eventFilter(obj, event)

    def showEvent(self, event):
        super().showEvent(event)
        self.update_filter_buttons()

    def sectionResized(self, logicalIndex, oldSize, newSize):
        super().sectionResized(logicalIndex, oldSize, newSize)
        self.update_filter_buttons()
        if self.isInteractive():
            QTimer.singleShot(10, self.update_filter_buttons)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_filter_buttons()

    def update_filter_buttons(self):
        if not self.viewport() or not self.isVisible():
            return
        for section in range(self.count()):
            if section == 0: # 跳过复选框列
                continue
            if section not in self.filter_buttons:
                button = FilterButton(self.viewport())
                button.setToolTip("点击筛选")
                # 使用 lambda 确保传递正确的 section
                button.clicked.connect(lambda checked, s=section: self.show_filter_menu(s))
                self.filter_buttons[section] = button

            button = self.filter_buttons[section]

            # 检查是否有活动的过滤器来设置按钮状态
            is_active = False
            if section in self.filters:
                # 确保 all_column_values 已经填充
                if section in self.all_column_values:
                    unique_vals = self.all_column_values[section].get('values', set())
                    has_empty = self.all_column_values[section].get('has_empty', False)
                    total_unique_count = len(unique_vals)
                    # 如果有空值且 "(空)" 不在 unique_vals 里（通常 collect_all_values 会添加）
                    # if has_empty and "(空)" not in unique_vals:
                    #     total_unique_count += 1
                    if len(self.filters[section]) < total_unique_count:
                        is_active = True
                else: # 如果还没有收集值，但有过滤器，也认为是激活状态
                    is_active = True
            button.set_active(is_active)

            # 计算和设置按钮位置
            section_pos = self.sectionViewportPosition(section)
            section_width = self.sectionSize(section)
            pos_x = section_pos + section_width - button.width() - 5
            pos_y = (self.height() - button.height()) // 2
            if pos_x < 0 or pos_x + button.width() > self.viewport().width() or section_pos > self.viewport().width():
                button.hide()
            else:
                button.move(pos_x, pos_y)
                button.show()
                button.raise_()

    def show_filter_menu(self, section):
        table = self.parentWidget()
        if not table or not isinstance(table, QTableWidget):
            return
        # 确保在显示菜单前收集最新的值
        self.all_column_values[section] = self.collect_all_values(table, section)
        unique_values = self.all_column_values[section]['values'] # 包含 "(空)"
        has_empty = self.all_column_values[section]['has_empty']
        button = self.filter_buttons[section]
        popup = FilterPopup(table, section, unique_values, has_empty)

        # 恢复之前的选择状态
        if section in self.filters:
            checked_items = self.filters[section]
            popup.checked_items = set(checked_items) # 确保是集合副本
            # 更新弹窗内复选框状态
            for value, cb in popup.checkboxes.items():
                cb.setChecked(value in checked_items)
            # 更新全选框状态
            all_selected = len(checked_items) == len(popup.unique_values_with_empty)
            popup.selectAll.setChecked(all_selected)
        else:
             # 如果没有历史过滤器，确保弹窗内是全选状态
             popup.checked_items = set(popup.unique_values_with_empty)
             for cb in popup.checkboxes.values():
                 cb.setChecked(True)
             popup.selectAll.setChecked(True)

        pos = button.mapToGlobal(QPoint(0, button.height()))
        popup.move(pos)
        result = popup.exec_()
        if result == QDialog.Accepted:
            self.filters[section] = popup.checked_items
            self.apply_filters(table)
            # 更新按钮状态
            button.set_active(len(popup.checked_items) < len(popup.unique_values_with_empty))
            self.filter_applied.emit() # 发射信号
        # 如果用户取消，则不做任何事情

    def collect_all_values(self, table, section):
        row_hidden_states = []
        # 记录隐藏状态并暂时显示所有行
        for row in range(table.rowCount()):
            row_hidden_states.append(table.isRowHidden(row))
            table.setRowHidden(row, False)

        unique_values = set()
        has_empty = False
        # 收集值
        for row in range(table.rowCount()):
            item = table.item(row, section)
            value = item.text().strip() if item and item.text().strip() else None # 视空字符串为空
            if value is not None:
                unique_values.add(value)
            else:
                has_empty = True

        # 恢复隐藏状态
        for row in range(table.rowCount()):
            table.setRowHidden(row, row_hidden_states[row])

        # 将实际的空值表示为 "(空)" 字符串以便于 UI 处理
        collected_set = set(unique_values) # 复制一份非空值
        if has_empty:
            collected_set.add("(空)")
        # 返回包含唯一值（含"(空)"）和是否有空值标志的字典
        return {'values': collected_set, 'has_empty': has_empty}


    def apply_filters(self, table):
        # 先显示所有行
        for row in range(table.rowCount()):
            table.setRowHidden(row, False)
        # 再根据筛选条件隐藏
        for row in range(table.rowCount()):
            should_show = True
            for section, checked_items in self.filters.items():
                item = table.item(row, section)
                # 获取单元格的值，将 None 或空白字符串处理为 "(空)"
                value_text = item.text().strip() if item and item.text() else None
                value_for_filter = value_text if value_text is not None else "(空)"

                if value_for_filter not in checked_items:
                    should_show = False
                    break # 只要有一列不满足就隐藏
            table.setRowHidden(row, not should_show)

    def clear_filters(self):
        self.filters.clear()
        for button in self.filter_buttons.values():
            if isinstance(button, FilterButton):
                button.set_active(False)
        table = self.parentWidget()
        if table and isinstance(table, QTableWidget):
            for row in range(table.rowCount()):
                table.setRowHidden(row, False)
        self.filter_applied.emit() # 发射信号

class FilterPopup(QDialog):
    """过滤弹窗"""
    def __init__(self, parent, column, unique_values, has_empty=False):
        super().__init__(parent, Qt.Popup)
        self.column = column
        # unique_values 应该已经包含了 "(空)" (如果 has_empty is True)
        self.unique_values_with_empty = set(unique_values)
        # 初始时 checked_items 应该与传入的过滤器状态一致，或者全选
        # 这里先默认为全选，在 show_filter_menu 中会根据 self.filters 恢复状态
        self.checked_items = set(self.unique_values_with_empty)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.setStyleSheet("""
            QDialog { background-color: #FFFFFF; border: 1px solid #CCCCCC; border-radius: 4px; }
            QCheckBox { padding: 3px; border-radius: 2px; }
            QCheckBox:hover { background-color: #F0F8FF; }
            QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #F6F6F6, stop:1 #E6E6E6); border: 1px solid #CCCCCC; border-radius: 3px; min-height: 24px; padding: 0 10px; color: #333333; }
            QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #FFFFFF, stop:1 #F0F0F0); }
            QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #E0E0E0, stop:1 #D0D0D0); }
            QScrollArea { border: 1px solid #DDDDDD; border-radius: 2px; background-color: #FAFAFA; }
            QLineEdit { border: 1px solid #CCCCCC; border-radius: 3px; padding: 3px; background-color: #FFFFFF; }
            QLineEdit:focus { border: 1px solid #90CAF9; }
        """)
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索...")
        self.search_input.textChanged.connect(self.filter_items)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        self.selectAll = QCheckBox("全选")
        # 初始状态由 show_filter_menu 决定，这里先设为 True
        self.selectAll.setChecked(True)
        font = self.selectAll.font()
        font.setBold(True)
        self.selectAll.setFont(font)
        self.selectAll.stateChanged.connect(self.select_all_changed)
        layout.addWidget(self.selectAll)
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_content)
        self.checkboxes = {}

        # 对值进行排序，确保 "(空)" 在最上面
        # unique_values_with_empty 是 set，先转 list 再排序
        # 使用 lambda 确保 "(空)" 排在前面，其他按字典序
        values_list = sorted(list(self.unique_values_with_empty),
                               key=lambda x: (x != "(空)", str(x)))

        for value in values_list:
            cb = QCheckBox(str(value)) # 显示时转换为字符串
            # 初始状态由 show_filter_menu 决定
            cb.setChecked(value in self.checked_items)
            cb.stateChanged.connect(self.item_changed)
            self.scroll_layout.addWidget(cb)
            self.checkboxes[value] = cb # 使用原始值（可能是 "(空)"）作为键

        self.scroll_area.setWidget(self.scroll_content)
        layout.addWidget(self.scroll_area)
        button_layout = QHBoxLayout()
        self.apply_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        self.apply_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        self.setMinimumWidth(240)
        self.setMaximumHeight(400)

    def filter_items(self, text):
        search_text = text.lower()
        has_visible = False
        # 迭代 self.checkboxes.items() 来同时获取值和复选框
        for value, cb in self.checkboxes.items():
             # 搜索时对比复选框的文本（即str(value)）
            is_visible = search_text in cb.text().lower()
            cb.setVisible(is_visible)
            if is_visible:
                has_visible = True
        # 过滤后需要重新评估全选框状态
        self.update_select_all_state()

    def select_all_changed(self, state):
        is_checked = (state == Qt.Checked)
        # 更新所有可见复选框的状态
        something_changed = False
        for value, cb in self.checkboxes.items():
            # 只改变可见的复选框状态
            if cb.isVisible():
                if cb.isChecked() != is_checked:
                    cb.blockSignals(True)
                    cb.setChecked(is_checked)
                    cb.blockSignals(False)
                    something_changed = True
                    # 更新 self.checked_items 集合
                    if is_checked:
                        self.checked_items.add(value)
                    else:
                        self.checked_items.discard(value)
        # 如果状态实际改变了，可以触发后续更新，虽然这里不需要
        # if something_changed:
        #     pass

    def item_changed(self, state):
        sender = self.sender() # 获取信号发送者（QCheckBox）
        value = sender.text() # 获取复选框显示的文本

        # 根据文本找到原始值（键），处理 "(空)" 的情况
        original_value = None
        for k, v in self.checkboxes.items():
            if v is sender:
                original_value = k
                break

        if original_value is None:
             print(f"Warning: Could not find original value for checkbox text '{value}'")
             return # 未找到对应值，提前返回

        if state == Qt.Checked:
            self.checked_items.add(original_value)
        else:
            self.checked_items.discard(original_value)

        # 单个项改变后，更新全选框的状态
        self.update_select_all_state()

    def update_select_all_state(self):
        """根据当前可见项的选择状态更新全选框"""
        all_visible_selected = True
        visible_item_count = 0
        for value, cb in self.checkboxes.items():
            if cb.isVisible():
                visible_item_count += 1
                if not cb.isChecked(): # 直接检查复选框状态
                    all_visible_selected = False
                    # break # 不必break，需要计数

        # 如果没有可见项，则取消全选状态
        if visible_item_count == 0:
            all_visible_selected = False

        # 更新 "全选" 复选框的状态，不触发其 stateChanged 信号
        self.selectAll.blockSignals(True)
        self.selectAll.setChecked(all_visible_selected)
        self.selectAll.blockSignals(False)


# --- 返修统计对话框 ---

class RepairingStatsDialog(QDialog):
    """返修统计对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = Database() # 创建数据库连接实例
        # 创建设置对象，用于保存/加载列宽和窗口状态
        self.settings = QSettings("UPTEC", "MachiningOrderManagement")
        self.setWindowTitle("返修统计")
        self.resize(1200, 700)
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        self.load_data() # 加载数据
        self.load_column_widths() # 加载保存的列宽和窗口状态

    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout(self)

        # 按钮区域
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加")
        self.add_btn.clicked.connect(self.add_new_repair_row)
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_selected_repair_rows)
        self.select_all_box = QCheckBox("全选")
        self.select_all_box.stateChanged.connect(self.select_all_changed)
        self.clear_filter_btn = QPushButton("清除筛选")
        self.clear_filter_btn.clicked.connect(self.clear_all_filters)
        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self.export_selected_rows)

        # 调整按钮顺序：全选框最左
        button_layout.addWidget(self.select_all_box)
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.clear_filter_btn)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(12)
        self.db_field_to_header = {
            'ProjectNumber': "项目号",
            'MaterialNumber': "物料号",
            'MaterialName': "名称",
            'Qty': "数量",
            'RepairingContent': "返修内容",
            'ArrivingDate': "来料日期",
            'FinishDate': "完工日期",
            'RepairingPerson': "机加返修人",
            'RepairingTime': "单件返修工时/小时",
            'TotalTime': "总时间",
            'Classification': "分类"
        }
        self.header_order = [
            "项目号", "物料号", "名称", "数量", "返修内容", "来料日期",
            "完工日期", "机加返修人", "单件返修工时/小时", "总时间", "分类"
        ]
        display_headers = [""] + self.header_order
        self.table.setHorizontalHeaderLabels(display_headers)

        filter_header = FilterHeaderView(Qt.Horizontal, self.table)
        self.table.setHorizontalHeader(filter_header)
        filter_header.filter_applied.connect(self.on_filter_applied)

        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)
        self.table.setColumnWidth(0, 40)

        # 初始宽度设置移到 load_column_widths，如果没有保存则使用默认值

        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSortingEnabled(False)
        self.table.setEditTriggers(QAbstractItemView.DoubleClicked | QAbstractItemView.EditKeyPressed)
        self.table.cellChanged.connect(self.cell_changed)

        main_layout.addWidget(self.table)

    def load_data(self):
        """从数据库加载返修数据"""
        # 禁用更新和信号，提高加载性能
        self.table.setUpdatesEnabled(False)
        self.table.blockSignals(True)

        self.table.setRowCount(0) # 清空表格
        self.select_all_box.setChecked(False) # 取消全选状态

        try:
            repairing_data = self.db.get_all_repairing_records()
        except Exception as e:
            QMessageBox.critical(self, "数据库错误", f"加载返修数据失败: {e}")
            print(f"加载返修数据错误: {e}")
            repairing_data = [] # 出错时使用空列表
        finally:
            # 确保信号和更新最终被启用
            self.table.blockSignals(False)
            self.table.setUpdatesEnabled(True)

        if not repairing_data:
            print("数据库中没有返修记录")
            # 清空筛选器缓存并更新按钮
            header = self.table.horizontalHeader()
            if isinstance(header, FilterHeaderView):
                 header.filters.clear()
                 header.all_column_values.clear()
                 QTimer.singleShot(0, header.update_filter_buttons)
            return

        self.table.setRowCount(len(repairing_data))

        # 创建字段名到列索引的映射，基于 self.header_order
        header_to_col_idx = {header: idx + 1 for idx, header in enumerate(self.header_order)}
        # 反向映射，用于从数据库字段找到表头名
        db_field_to_header_key = {v: k for k, v in self.db_field_to_header.items()}

        # 重新启用更新和信号
        self.table.setUpdatesEnabled(True)
        self.table.blockSignals(False)

        # 开始填充数据
        self.table.blockSignals(True) # 填充时再次阻止信号
        try:
            for row, data_row in enumerate(repairing_data):
                # 添加复选框
                checkbox = QCheckBox()
                checkbox_container = QWidget()
                layout = QHBoxLayout(checkbox_container)
                layout.addWidget(checkbox)
                layout.setAlignment(Qt.AlignCenter)
                layout.setContentsMargins(0, 0, 0, 0)
                self.table.setCellWidget(row, 0, checkbox_container)

                # 填充数据 - 使用 self.db_field_to_header 和 self.header_order 保证顺序
                for db_field, header_text in self.db_field_to_header.items():
                    col_idx = header_to_col_idx.get(header_text)
                    if col_idx is not None: # 确保表头存在于顺序列表中
                        # 从数据库记录中获取值，处理 None 和日期/时间类型
                        raw_value = data_row.get(db_field)
                        display_value = ""
                        if isinstance(raw_value, (datetime.date, datetime.datetime)):
                            # 格式化日期和时间
                            display_value = raw_value.strftime('%Y-%m-%d')
                        elif raw_value is not None:
                            display_value = str(raw_value)
                        # else: display_value 保持 ""

                        item = QTableWidgetItem(display_value)

                        # 对齐数字（假设数量、工时、总时间是数字）
                        if header_text in ["数量", "单件返修工时/小时", "总时间"]:
                            try:
                                # 尝试转换为浮点数以确定是否为数字
                                float(display_value if display_value else "0") # 空字符串视为0
                                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                            except ValueError:
                                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

                        # 在第一列（项目号列，索引为 1）存储数据库 ID
                        if col_idx == 1: # 假设第一数据列是项目号
                            record_id = data_row.get('id') # 获取数据库记录的 ID
                            if record_id is not None:
                                item.setData(Qt.UserRole, record_id)
                            else:
                                print(f"警告: 行 {row} 缺少数据库 ID")

                        self.table.setItem(row, col_idx, item)
                    else:
                        print(f"警告: 找不到表头 '{header_text}' 对应的列索引。")
        finally:
            self.table.blockSignals(False) # 填充完毕，恢复信号

        # 数据加载后更新筛选器的值
        header = self.table.horizontalHeader()
        if isinstance(header, FilterHeaderView):
            # 清除旧的筛选值缓存
            header.filters.clear() # 清除现有筛选条件
            header.all_column_values.clear()
            # 延迟更新按钮，确保表格已渲染
            QTimer.singleShot(50, header.update_filter_buttons)

    def select_all_changed(self, state):
        """全选复选框状态改变事件"""
        is_checked = (state == Qt.Checked)
        # 阻止表格信号
        self.table.blockSignals(True)
        try:
            for row in range(self.table.rowCount()):
                # 只影响可见行
                if not self.table.isRowHidden(row):
                    checkbox_container = self.table.cellWidget(row, 0)
                    if checkbox_container:
                        checkbox = checkbox_container.findChild(QCheckBox)
                        if checkbox and checkbox.isChecked() != is_checked:
                            checkbox.setChecked(is_checked)
        finally:
            self.table.blockSignals(False)

    def export_selected_rows(self):
        """导出选中的行到Excel"""
        selected_rows_data = []
        # 使用 self.header_order 来确定导出的列和顺序
        export_headers = self.header_order
        # 创建表头到列索引的映射
        header_to_col_idx = {header: idx + 1 for idx, header in enumerate(export_headers)}

        for row in range(self.table.rowCount()):
            # 只导出可见且选中的行
            if not self.table.isRowHidden(row):
                checkbox_container = self.table.cellWidget(row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        row_data = {}
                        for header in export_headers:
                            col_idx = header_to_col_idx.get(header)
                            if col_idx is not None:
                                item = self.table.item(row, col_idx)
                                row_data[header] = item.text() if item else ""
                            else:
                                row_data[header] = "" # 以防万一
                        selected_rows_data.append(row_data)

        if not selected_rows_data:
            QMessageBox.warning(self, "警告", "请选择要导出的可见行！")
            return

        # 选择保存文件路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出返修数据",
            "返修数据导出.xlsx", # 默认文件名
            "Excel Files (*.xlsx)"
        )

        if not file_path:
            return # 用户取消

        if not file_path.endswith('.xlsx'):
            file_path += '.xlsx'

        try:
            import pandas as pd
            # 直接使用收集的数据创建 DataFrame，列顺序已保证
            df = pd.DataFrame(selected_rows_data, columns=export_headers)
            df.to_excel(file_path, index=False)
            QMessageBox.information(self, "成功", f"已成功导出 {len(selected_rows_data)} 行数据到 \n{file_path}")
        except ImportError:
             QMessageBox.critical(self, "错误", "导出Excel需要 pandas 库。请先安装：pip install pandas")
        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出Excel失败: {str(e)}")
            print(f"导出Excel错误: {e}")

    def clear_all_filters(self):
        """清除所有筛选条件"""
        header = self.table.horizontalHeader()
        if isinstance(header, FilterHeaderView):
            header.clear_filters() # clear_filters 内部会发射 filter_applied 信号

    def on_filter_applied(self):
        """筛选条件应用或清除后的处理"""
        # 清除全选框的状态，因为可见行已改变
        self.select_all_box.setChecked(False)
        print("筛选条件已应用或清除")

    def add_row_to_table(self, record, insert_pos=0):
        """将一条记录添加到表格的指定位置 (默认顶部)"""
        self.table.blockSignals(True)
        try:
            self.table.insertRow(insert_pos)

            # 添加复选框
            checkbox = QCheckBox()
            checkbox_container = QWidget()
            layout = QHBoxLayout(checkbox_container)
            layout.addWidget(checkbox)
            layout.setAlignment(Qt.AlignCenter)
            layout.setContentsMargins(0, 0, 0, 0)
            self.table.setCellWidget(insert_pos, 0, checkbox_container)

            # 填充数据
            header_to_col_idx = {header: idx + 1 for idx, header in enumerate(self.header_order)}
            for db_field, header_text in self.db_field_to_header.items():
                col_idx = header_to_col_idx.get(header_text)
                if col_idx is not None:
                    raw_value = record.get(db_field)
                    display_value = ""
                    if isinstance(raw_value, (datetime.date, datetime.datetime)):
                        display_value = raw_value.strftime('%Y-%m-%d')
                    elif raw_value is not None:
                        display_value = str(raw_value)

                    item = QTableWidgetItem(display_value)
                    # Set alignment based on header
                    if header_text in ["数量", "单件返修工时/小时", "总时间"]:
                        try:
                            float(display_value if display_value else "0")
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        except ValueError:
                             item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    else:
                         item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

                    # 在第一列（项目号列，索引为 1）存储数据库 ID
                    if col_idx == 1: # 假设第一数据列是项目号
                        record_id = record.get('id') # 获取新记录的 ID
                        if record_id is not None:
                            item.setData(Qt.UserRole, record_id)
                        else:
                            print(f"警告: 新添加的行缺少数据库 ID")

                    self.table.setItem(insert_pos, col_idx, item)

            # 滚动到新添加的行
            self.table.scrollToItem(self.table.item(insert_pos, 1), QAbstractItemView.PositionAtTop)
            # 使新行变为可编辑状态需要额外逻辑 (设置 edit triggers, 保存更改等)
            # self.table.editItem(self.table.item(insert_pos, 1))

        finally:
            self.table.blockSignals(False)

    def add_new_repair_row(self):
        """添加新的返修记录行 (添加到末尾)"""
        print("尝试添加新返修记录...")
        try:
            new_record = self.db.add_empty_repairing_record()
            if new_record:
                # 将新记录添加到表格末尾
                current_row_count = self.table.rowCount()
                self.add_row_to_table(new_record, current_row_count)
                # 更新筛选器按钮状态
                header = self.table.horizontalHeader()
                if isinstance(header, FilterHeaderView):
                     QTimer.singleShot(50, header.update_filter_buttons)
                print("新行已添加到UI末尾")
            else:
                QMessageBox.warning(self, "添加失败", "无法在数据库中创建新的返修记录。\n请检查数据库连接和表结构。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加新行时出错: {e}")
            print(f"添加新行错误: {e}")

    def delete_selected_repair_rows(self):
        """删除选中的返修记录行"""
        selected_rows_info = [] # 存储 (row_index, record_id)
        for row in range(self.table.rowCount()):
            # 只考虑可见行
            if not self.table.isRowHidden(row):
                checkbox_container = self.table.cellWidget(row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # 从第一列（项目号列）获取存储的 ID
                        id_item = self.table.item(row, 1)
                        if id_item:
                            record_id = id_item.data(Qt.UserRole)
                            if record_id is not None:
                                selected_rows_info.append((row, record_id))
                            else:
                                print(f"警告: 行 {row} 的第一列缺少 record_id")
                        else:
                             print(f"警告: 行 {row} 缺少第一列 item")

        if not selected_rows_info:
            QMessageBox.warning(self, "警告", "请选择要删除的可见行！")
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_rows_info)} 条返修记录吗？\n此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            ids_to_delete = [record_id for _, record_id in selected_rows_info]
            if not ids_to_delete:
                QMessageBox.warning(self, "错误", "未能获取要删除记录的ID。")
                return

            try:
                success = self.db.delete_repairing_records(ids_to_delete)
                if success:
                    # 从UI表格中删除行（从后往前，避免索引问题）
                    rows_to_delete = sorted([row_idx for row_idx, _ in selected_rows_info], reverse=True)
                    self.table.blockSignals(True)
                    try:
                        for row_idx in rows_to_delete:
                            self.table.removeRow(row_idx)
                    finally:
                        self.table.blockSignals(False)

                    self.select_all_box.setChecked(False) # 取消全选
                    QMessageBox.information(self, "成功", "选中的返修记录已删除。")
                    # 删除后可能需要更新筛选器的唯一值
                    header = self.table.horizontalHeader()
                    if isinstance(header, FilterHeaderView):
                        # 清除缓存并更新按钮可能更简单
                        header.all_column_values.clear()
                        QTimer.singleShot(50, header.update_filter_buttons)
                else:
                    QMessageBox.warning(self, "删除失败", "数据库删除操作失败，请检查日志或联系管理员。")
            except Exception as e:
                QMessageBox.critical(self, "删除错误", f"删除过程中发生错误: {e}")
                print(f"删除返修记录时出错: {e}")

    def cell_changed(self, row, column):
        """处理单元格编辑后的数据保存"""
        if column == 0: return

        id_item = self.table.item(row, 1)
        if not id_item: return
        record_id = id_item.data(Qt.UserRole)
        if record_id is None: return

        changed_item = self.table.item(row, column)
        new_value = changed_item.text().strip() if changed_item else "" # 处理空单元格和前后空格

        if column < 1 or column >= len(self.header_order) + 1: return
        header_text = self.header_order[column - 1]
        db_field = None
        for field, header in self.db_field_to_header.items():
            if header == header_text:
                db_field = field
                break
        if db_field is None: return

        print(f"准备更新返修记录 ID: {record_id}, 字段: {db_field}, 新值: '{new_value}'")
        try:
            # 调用添加到 database.py 的新方法
            success = self.db.update_repairing_record(record_id, db_field, new_value)

            if success:
                print(f"记录 {record_id} 字段 {db_field} 更新成功")
                # 更新筛选器缓存和按钮状态
                header_view = self.table.horizontalHeader()
                if isinstance(header_view, FilterHeaderView):
                    # 简单方式：清除该列缓存并更新按钮
                    header_view.all_column_values.pop(column, None)
                    QTimer.singleShot(0, lambda c=column: self.update_filter_after_edit(c))
            else:
                QMessageBox.warning(self, "更新失败", f"无法更新记录 {record_id} 的字段 {db_field}。数据库操作失败。")
                # 可以尝试恢复原始值，但需要额外逻辑
        except Exception as e:
             QMessageBox.critical(self, "更新错误", f"更新记录时出错: {e}")
             print(f"单元格修改更新数据库错误: {e}")
             # 回滚UI?

    def update_filter_after_edit(self, column):
        """编辑后更新筛选器状态 (避免立即收集所有值)"""
        header_view = self.table.horizontalHeader()
        if isinstance(header_view, FilterHeaderView):
             # 重新收集该列的值并更新按钮
             header_view.all_column_values[column] = header_view.collect_all_values(self.table, column)
             header_view.update_filter_buttons()

    # --- 添加用于列宽和窗口状态持久化的方法 --- 
    def save_column_widths(self):
        """保存表格列宽和窗口状态到用户配置"""
        if not self.table:
            return
        print("保存返修窗口列宽和状态...")
        # 保存列宽，使用特定前缀
        for col in range(self.table.columnCount()):
            width = self.table.columnWidth(col)
            self.settings.setValue(f"RepairingDialog/col{col}_width", width)
            
        # 保存窗口大小和位置
        self.settings.setValue("RepairingDialog/size", self.size())
        self.settings.setValue("RepairingDialog/pos", self.pos())
    
    def load_column_widths(self):
        """从用户配置加载表格列宽和窗口状态"""
        if not self.table:
            return
        print("加载返修窗口列宽和状态...")
        # 定义默认列宽（如果未保存设置）
        default_widths = {
            0: 40, 1: 100, 2: 100, 3: 120, 4: 60, 5: 150, 6: 100,
            7: 100, 8: 100, 9: 120, 10: 80, 11: 100
        }
        # 加载列宽
        for col in range(self.table.columnCount()):
            width_key = f"RepairingDialog/col{col}_width"
            # 从设置获取，如果不存在则使用默认值
            default_width = default_widths.get(col, 100) # 未在default_widths中定义的列给100
            width = self.settings.value(width_key, default_width, type=int)
            if width > 0: # 确保宽度有效
                self.table.setColumnWidth(col, width)
        
        # 加载窗口大小和位置
        if self.settings.contains("RepairingDialog/size"):
            size = self.settings.value("RepairingDialog/size")
            if isinstance(size, QSize): # 确保类型正确
                self.resize(size)
        
        if self.settings.contains("RepairingDialog/pos"):
            pos = self.settings.value("RepairingDialog/pos")
            if isinstance(pos, QPoint): # 确保类型正确
                self.move(pos)

    # 重写关闭/接受/拒绝事件以保存状态
    def closeEvent(self, event):
        self.save_column_widths()
        super().closeEvent(event)
        
    def accept(self):
        self.save_column_widths()
        super().accept()
        
    def reject(self):
        self.save_column_widths()
        super().reject()

# --- 用于独立测试 ---
if __name__ == '__main__':
    app = QApplication(sys.argv)
    dialog = RepairingStatsDialog()
    dialog.show()
    sys.exit(app.exec_())
